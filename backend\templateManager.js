// AI PPT Generator - 模板管理器
const fs = require('fs');
const path = require('path');

class TemplateManager {
    constructor() {
        this.templateDir = path.join(__dirname, '../AiTemplate');
        this.templates = [];
        this.loadTemplates();
    }

    // 加载模板目录中的所有模板
    loadTemplates() {
        try {
            if (!fs.existsSync(this.templateDir)) {
                console.log('模板目录不存在，创建默认模板配置');
                this.createDefaultTemplates();
                return;
            }

            const files = fs.readdirSync(this.templateDir);
            const pptFiles = files.filter(file => 
                file.toLowerCase().endsWith('.pptx') || 
                file.toLowerCase().endsWith('.ppt')
            );

            this.templates = pptFiles.map((file, index) => {
                const filePath = path.join(this.templateDir, file);
                const stats = fs.statSync(filePath);
                
                return {
                    id: index + 1,
                    name: this.getTemplateName(file),
                    filename: file,
                    path: filePath,
                    category: this.getTemplateCategory(file),
                    description: this.getTemplateDescription(file),
                    layout: this.getTemplateLayout(file),
                    size: stats.size,
                    createdAt: stats.birthtime,
                    modifiedAt: stats.mtime
                };
            });

            console.log(`✅ 成功加载 ${this.templates.length} 个PPT模板`);
            this.templates.forEach(template => {
                console.log(`   - ${template.name} (${template.filename})`);
            });

        } catch (error) {
            console.error('加载模板时出错:', error);
            this.createDefaultTemplates();
        }
    }

    // 根据文件名推断模板名称
    getTemplateName(filename) {
        const nameWithoutExt = path.parse(filename).name;
        
        // 尝试从文件名中提取有意义的名称
        if (nameWithoutExt.includes('business') || nameWithoutExt.includes('商务')) {
            return '商务报告模板';
        } else if (nameWithoutExt.includes('education') || nameWithoutExt.includes('教育')) {
            return '教育培训模板';
        } else if (nameWithoutExt.includes('creative') || nameWithoutExt.includes('创意')) {
            return '创意展示模板';
        } else if (nameWithoutExt.includes('simple') || nameWithoutExt.includes('简约')) {
            return '简约风格模板';
        } else {
            // 使用文件名作为模板名，但进行美化
            return nameWithoutExt.replace(/[-_]/g, ' ').replace(/\d+/g, '').trim() || '通用模板';
        }
    }

    // 根据文件名推断模板类别
    getTemplateCategory(filename) {
        const name = filename.toLowerCase();
        
        if (name.includes('business') || name.includes('商务') || name.includes('report')) {
            return 'business';
        } else if (name.includes('education') || name.includes('教育') || name.includes('training')) {
            return 'education';
        } else if (name.includes('creative') || name.includes('创意') || name.includes('design')) {
            return 'creative';
        } else {
            return 'general';
        }
    }

    // 根据类别生成描述
    getTemplateDescription(filename) {
        const category = this.getTemplateCategory(filename);
        
        const descriptions = {
            'business': '适用于商务演示、工作报告和企业展示',
            'education': '适用于教学课件、培训材料和学术演示',
            'creative': '适用于创意展示、设计作品和艺术演示',
            'general': '通用模板，适用于各种场景'
        };

        return descriptions[category] || descriptions['general'];
    }

    // 根据类别确定布局风格
    getTemplateLayout(filename) {
        const category = this.getTemplateCategory(filename);
        
        const layouts = {
            'business': 'professional',
            'education': 'educational',
            'creative': 'creative',
            'general': 'standard'
        };

        return layouts[category] || layouts['general'];
    }

    // 创建默认模板配置（当没有实际模板文件时）
    createDefaultTemplates() {
        this.templates = [
            {
                id: 1,
                name: '商务报告模板',
                filename: 'business-template.pptx',
                path: null,
                category: 'business',
                description: '适用于商务演示、工作报告和企业展示',
                layout: 'professional',
                size: 0,
                createdAt: new Date(),
                modifiedAt: new Date(),
                isDefault: true
            },
            {
                id: 2,
                name: '教育培训模板',
                filename: 'education-template.pptx',
                path: null,
                category: 'education',
                description: '适用于教学课件、培训材料和学术演示',
                layout: 'educational',
                size: 0,
                createdAt: new Date(),
                modifiedAt: new Date(),
                isDefault: true
            },
            {
                id: 3,
                name: '创意展示模板',
                filename: 'creative-template.pptx',
                path: null,
                category: 'creative',
                description: '适用于创意展示、设计作品和艺术演示',
                layout: 'creative',
                size: 0,
                createdAt: new Date(),
                modifiedAt: new Date(),
                isDefault: true
            }
        ];

        console.log('📝 使用默认模板配置');
    }

    // 获取所有模板
    getAllTemplates() {
        return this.templates;
    }

    // 根据ID获取模板
    getTemplateById(id) {
        return this.templates.find(template => template.id === parseInt(id));
    }

    // 根据类别获取模板
    getTemplatesByCategory(category) {
        return this.templates.filter(template => template.category === category);
    }

    // 重新加载模板（用于动态更新）
    reloadTemplates() {
        console.log('🔄 重新加载模板...');
        this.loadTemplates();
        return this.templates;
    }

    // 获取模板统计信息
    getTemplateStats() {
        const stats = {
            total: this.templates.length,
            categories: {},
            totalSize: 0
        };

        this.templates.forEach(template => {
            // 统计类别
            if (!stats.categories[template.category]) {
                stats.categories[template.category] = 0;
            }
            stats.categories[template.category]++;

            // 统计总大小
            stats.totalSize += template.size || 0;
        });

        return stats;
    }

    // 验证模板文件是否存在
    validateTemplate(templateId) {
        const template = this.getTemplateById(templateId);
        if (!template) {
            return { valid: false, error: '模板不存在' };
        }

        if (template.isDefault) {
            return { valid: true, template, isDefault: true };
        }

        if (!template.path || !fs.existsSync(template.path)) {
            return { valid: false, error: '模板文件不存在' };
        }

        return { valid: true, template };
    }
}

module.exports = TemplateManager;
