const express = require('express');
const bodyParser = require('body-parser');
const cors = require('cors');
const path = require('path');
const fs = require('fs');
const { spawn } = require('child_process');
const TemplateManager = require('./templateManager');
const PPTParser = require('./pptParser');
const PPTExporter = require('./pptExporter');

const app = express();
const PORT = process.env.PORT || 3001;

// 初始化模板管理器、PPT解析器和导出器
const templateManager = new TemplateManager();
const pptParser = new PPTParser();
const pptExporter = new PPTExporter();

// 中间件配置
app.use(cors());
app.use(bodyParser.json({ limit: '10mb' }));
app.use(bodyParser.urlencoded({ extended: true, limit: '10mb' }));

// 添加请求日志中间件
app.use((req, res, next) => {
    console.log(`📁 ${req.method} ${req.url}`);
    next();
});

// 静态文件服务配置
app.use(express.static(path.join(__dirname, '../frontend'), {
    setHeaders: (res, filePath) => {
        console.log(`📄 服务静态文件: ${filePath}`);
        if (filePath.endsWith('.css')) {
            res.setHeader('Content-Type', 'text/css; charset=utf-8');
            console.log('🎨 设置CSS MIME类型');
        } else if (filePath.endsWith('.js')) {
            res.setHeader('Content-Type', 'application/javascript; charset=utf-8');
            console.log('📜 设置JS MIME类型');
        } else if (filePath.endsWith('.html')) {
            res.setHeader('Content-Type', 'text/html; charset=utf-8');
            console.log('📝 设置HTML MIME类型');
        }
    }
}));

// 设置API响应头确保正确的字符编码
app.use('/api', (req, res, next) => {
    res.setHeader('Content-Type', 'application/json; charset=utf-8');
    next();
});

// 获取所有模板
app.get('/api/templates', (req, res) => {
  try {
    const templates = templateManager.getAllTemplates();
    const stats = templateManager.getTemplateStats();

    res.json({
      success: true,
      templates,
      stats,
      message: `成功获取 ${templates.length} 个模板`
    });
  } catch (error) {
    console.error('获取模板失败:', error);
    res.status(500).json({
      success: false,
      error: '获取模板失败'
    });
  }
});

// 根据ID获取特定模板
app.get('/api/templates/:id', (req, res) => {
  try {
    const templateId = parseInt(req.params.id);
    const validation = templateManager.validateTemplate(templateId);

    if (!validation.valid) {
      return res.status(404).json({
        success: false,
        error: validation.error
      });
    }

    res.json({
      success: true,
      template: validation.template
    });
  } catch (error) {
    console.error('获取模板详情失败:', error);
    res.status(500).json({
      success: false,
      error: '获取模板详情失败'
    });
  }
});

// 重新加载模板
app.post('/api/templates/reload', (req, res) => {
  try {
    const templates = templateManager.reloadTemplates();
    res.json({
      success: true,
      templates,
      message: `重新加载完成，共 ${templates.length} 个模板`
    });
  } catch (error) {
    console.error('重新加载模板失败:', error);
    res.status(500).json({
      success: false,
      error: '重新加载模板失败'
    });
  }
});

// 预览PPT模板文件
app.get('/api/templates/:id/preview', async (req, res) => {
  try {
    const templateId = parseInt(req.params.id);
    const validation = templateManager.validateTemplate(templateId);

    if (!validation.valid) {
      return res.status(404).json({
        success: false,
        error: validation.error
      });
    }

    const template = validation.template;

    // 如果是默认模板，返回示例内容
    if (template.isDefault) {
      return res.json({
        success: true,
        isDefault: true,
        template: template,
        slides: generateDefaultPreview(template)
      });
    }

    // 解析真实的PPT文件
    console.log(`🔍 开始解析PPT模板: ${template.path}`);
    const pptData = await pptParser.parsePPTFile(template.path);

    res.json({
      success: true,
      isDefault: false,
      template: template,
      ...pptData
    });

  } catch (error) {
    console.error('PPT预览失败:', error);
    res.status(500).json({
      success: false,
      error: '生成PPT预览时发生错误'
    });
  }
});

// 导出PPT为PPTX文件
app.post('/api/export-ppt', async (req, res) => {
  try {
    const { slides, metadata, format } = req.body;

    if (!slides || !Array.isArray(slides) || slides.length === 0) {
      return res.status(400).json({
        success: false,
        error: '请提供要导出的幻灯片数据'
      });
    }

    console.log(`📤 开始导出PPT: ${slides.length} 张幻灯片`);

    // 根据格式选择导出方法
    let exportResult;

    switch (format) {
      case 'pptx':
      default:
        exportResult = await pptExporter.exportToPPTX(slides, metadata);
        break;
    }

    if (exportResult.success) {
      // 清理旧文件
      pptExporter.cleanupOldFiles(10);

      res.json({
        success: true,
        message: 'PPT导出成功',
        file: {
          name: exportResult.fileName,
          size: exportResult.fileSize,
          slideCount: exportResult.slideCount,
          downloadUrl: `/api/download/${exportResult.fileName}`
        }
      });
    } else {
      res.status(500).json({
        success: false,
        error: '导出失败'
      });
    }

  } catch (error) {
    console.error('PPT导出错误:', error);
    res.status(500).json({
      success: false,
      error: '导出PPT时发生错误: ' + error.message
    });
  }
});

// 下载导出的PPT文件
app.get('/api/download/:filename', (req, res) => {
  try {
    const filename = req.params.filename;
    const filePath = path.join(__dirname, '../exports', filename);

    // 检查文件是否存在
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({
        success: false,
        error: '文件不存在'
      });
    }

    // 设置响应头
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.presentationml.presentation');
    res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(filename)}"`);

    // 发送文件
    const fileStream = fs.createReadStream(filePath);
    fileStream.pipe(res);

    console.log(`📥 文件下载: ${filename}`);

  } catch (error) {
    console.error('文件下载错误:', error);
    res.status(500).json({
      success: false,
      error: '下载文件时发生错误'
    });
  }
});

// 获取导出文件列表
app.get('/api/exports', (req, res) => {
  try {
    const files = pptExporter.getExportedFiles();

    res.json({
      success: true,
      files: files.map(file => ({
        name: file.name,
        size: file.size,
        created: file.created,
        downloadUrl: `/api/download/${file.name}`
      }))
    });

  } catch (error) {
    console.error('获取导出文件列表失败:', error);
    res.status(500).json({
      success: false,
      error: '获取文件列表失败'
    });
  }
});

// 生成默认模板预览
function generateDefaultPreview(template) {
  return [
    {
      id: 1,
      type: 'title',
      title: template.name,
      subtitle: template.description,
      content: '',
      keywords: []
    },
    {
      id: 2,
      type: 'content',
      title: '模板特点',
      content: `类别: ${template.category}\n布局: ${template.layout}\n适用场景: ${template.description}`,
      keywords: [template.category, template.layout]
    },
    {
      id: 3,
      type: 'content',
      title: '使用说明',
      content: '1. 在输入框中输入您的内容\n2. 选择此模板\n3. 点击生成PPT按钮\n4. AI将基于此模板生成演示文稿',
      keywords: ['使用', '说明', '生成']
    }
  ];
}

// 生成PPT的主要API
app.post('/api/generate-ppt', async (req, res) => {
  try {
    const { text, templateId, options } = req.body;

    if (!text) {
      return res.status(400).json({
        success: false,
        error: '请提供要生成PPT的文本内容'
      });
    }

    // 验证模板
    const validation = templateManager.validateTemplate(templateId);
    if (!validation.valid) {
      return res.status(400).json({
        success: false,
        error: `模板错误: ${validation.error}`
      });
    }

    console.log('收到PPT生成请求:', {
      text: text.substring(0, 100) + '...',
      templateId,
      templateName: validation.template.name,
      options
    });

    // 调用Python AI模型进行内容分析
    const aiResult = await callPythonAI(text);

    // 根据AI分析结果和模板生成幻灯片
    const slides = await generateSlides(aiResult, validation.template, options);

    res.json({
      success: true,
      slides,
      metadata: {
        slideCount: slides.length,
        template: validation.template,
        templatePath: validation.template.path,
        isDefaultTemplate: validation.isDefault || false,
        generatedAt: new Date().toISOString(),
        aiAnalysis: {
          keywordCount: aiResult.keywords?.length || 0,
          sectionCount: aiResult.structure?.sections?.length || 0,
          sentiment: aiResult.sentiment,
          wordCount: aiResult.word_count
        }
      }
    });

  } catch (error) {
    console.error('PPT生成错误:', error);
    res.status(500).json({
      success: false,
      error: '生成PPT时发生错误，请稍后重试'
    });
  }
});

// 调用Python AI模型
function callPythonAI(text) {
  return new Promise((resolve, reject) => {
    // 确保文本编码正确
    const cleanText = Buffer.from(text, 'utf8').toString('utf8');

    const pythonProcess = spawn('python', [
      path.join(__dirname, '../models/nlp_processor.py'),
      cleanText
    ], {
      encoding: 'utf8',
      env: { ...process.env, PYTHONIOENCODING: 'utf-8' }
    });

    let result = '';
    let error = '';

    pythonProcess.stdout.on('data', (data) => {
      result += data.toString();
    });

    pythonProcess.stderr.on('data', (data) => {
      error += data.toString();
    });

    pythonProcess.on('close', (code) => {
      if (code === 0) {
        try {
          const parsedResult = JSON.parse(result);
          resolve(parsedResult);
        } catch (e) {
          reject(new Error('AI模型返回数据格式错误'));
        }
      } else {
        reject(new Error(`AI模型执行失败: ${error}`));
      }
    });
  });
}

// 生成幻灯片内容
async function generateSlides(aiResult, template, options = {}) {
  const { keywords, structure, sentiment } = aiResult;

  const slides = [
    {
      id: 1,
      type: 'title',
      title: structure.title || '演示文稿',
      subtitle: structure.subtitle || '基于AI生成',
      template: template.layout
    }
  ];

  // 根据AI分析的结构生成内容幻灯片
  if (structure.sections && structure.sections.length > 0) {
    structure.sections.forEach((section, index) => {
      slides.push({
        id: index + 2,
        type: 'content',
        title: section.title,
        content: section.content,
        keywords: section.keywords || [],
        template: template.layout
      });
    });
  }

  // 添加总结幻灯片
  slides.push({
    id: slides.length + 1,
    type: 'summary',
    title: '总结',
    content: keywords.slice(0, 5).join(' • '),
    template: template.layout
  });

  return slides;
}

// 健康检查
app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    message: 'AI PPT Generator API 运行正常',
    timestamp: new Date().toISOString()
  });
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`🚀 AI PPT Generator 服务器运行在 http://localhost:${PORT}`);
  console.log(`📊 API 文档: http://localhost:${PORT}/api/health`);
});

module.exports = app;
