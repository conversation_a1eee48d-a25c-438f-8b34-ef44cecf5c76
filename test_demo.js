// AI PPT Generator - 演示测试脚本

const axios = require('axios');

const API_BASE = 'http://localhost:3000/api';

async function testAPI() {
    console.log('🧪 开始测试 AI PPT Generator API...\n');

    try {
        // 测试健康检查
        console.log('1. 测试健康检查...');
        const healthResponse = await axios.get(`${API_BASE}/health`);
        console.log('✅ 健康检查通过:', healthResponse.data.message);
        console.log();

        // 测试获取模板
        console.log('2. 测试获取模板...');
        const templatesResponse = await axios.get(`${API_BASE}/templates`);
        console.log('✅ 模板获取成功，共', templatesResponse.data.templates.length, '个模板');
        templatesResponse.data.templates.forEach(template => {
            console.log(`   - ${template.name} (${template.category}): ${template.description}`);
        });
        console.log();

        // 测试PPT生成
        console.log('3. 测试PPT生成...');
        const testContent = `公司年度总结报告
2023年业绩回顾

一、销售业绩
今年销售额达到1000万元，同比增长20%
新客户获取率提升15%
老客户续约率达到90%

二、市场拓展
新开拓了5个城市市场
客户满意度达到95%
品牌知名度提升30%

三、未来规划
计划明年继续扩大市场份额
目标销售额增长30%
加强产品研发投入`;

        const generateResponse = await axios.post(`${API_BASE}/generate-ppt`, {
            text: testContent,
            templateId: 1,
            options: {
                autoLayout: true,
                includeImages: true,
                addTransitions: false
            }
        });

        if (generateResponse.data.success) {
            console.log('✅ PPT生成成功！');
            console.log(`   - 幻灯片数量: ${generateResponse.data.slides.length}`);
            console.log(`   - 使用模板: ${generateResponse.data.metadata.template.name}`);
            console.log(`   - 生成时间: ${generateResponse.data.metadata.generatedAt}`);
            
            console.log('\n📄 生成的幻灯片预览:');
            generateResponse.data.slides.forEach((slide, index) => {
                console.log(`   ${index + 1}. ${slide.title} (${slide.type})`);
                if (slide.keywords && slide.keywords.length > 0) {
                    console.log(`      关键词: ${slide.keywords.join(', ')}`);
                }
            });
        } else {
            console.log('❌ PPT生成失败:', generateResponse.data.error);
        }

    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        if (error.response) {
            console.error('   响应状态:', error.response.status);
            console.error('   响应数据:', error.response.data);
        }
    }

    console.log('\n🎉 测试完成！');
    console.log('💡 提示: 打开浏览器访问 http://localhost:3000 查看完整界面');
}

// 运行测试
testAPI();
