/* PPT Web预览器样式 */

.ppt-viewer {
    width: 100%;
    height: 100%;
    background: #f5f5f5;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

/* 工具栏 */
.ppt-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background: #2d3748;
    color: white;
    border-bottom: 1px solid #4a5568;
}

.toolbar-left,
.toolbar-center,
.toolbar-right {
    display: flex;
    align-items: center;
    gap: 10px;
}

.btn-icon {
    background: transparent;
    border: 1px solid #4a5568;
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.btn-icon:hover {
    background: #4a5568;
    border-color: #667eea;
}

.btn-icon:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.slide-counter {
    font-size: 14px;
    font-weight: 600;
    color: #e2e8f0;
    min-width: 60px;
    text-align: center;
}

.speed-select {
    background: #4a5568;
    border: 1px solid #667eea;
    color: white;
    padding: 6px 10px;
    border-radius: 4px;
    font-size: 12px;
}

/* 主要内容区域 */
.ppt-main {
    display: flex;
    height: calc(100% - 60px);
    position: relative;
}

.slide-container {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    background: #f7fafc;
    position: relative;
    overflow: hidden;
}

/* 幻灯片样式 */
.slide {
    width: 100%;
    max-width: 800px;
    aspect-ratio: 16/9;
    background: white;
    border-radius: 8px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    display: none;
    position: relative;
    overflow: hidden;
}

.slide.active {
    display: block;
    animation: slideIn 0.3s ease-in-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.slide-content {
    padding: 40px;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    position: relative;
}

/* 标题幻灯片 */
.slide-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 20px;
    text-align: center;
    line-height: 1.2;
}

.slide-subtitle {
    font-size: 1.5rem;
    font-weight: 400;
    color: #667eea;
    text-align: center;
    margin-bottom: 30px;
}

.slide-author,
.slide-date {
    font-size: 1rem;
    color: #718096;
    text-align: center;
    margin: 5px 0;
}

/* 内容幻灯片 */
.slide-content .slide-title {
    font-size: 2rem;
    text-align: left;
    border-bottom: 3px solid #667eea;
    padding-bottom: 15px;
    margin-bottom: 30px;
}

.slide-body {
    font-size: 1.2rem;
    line-height: 1.8;
    color: #4a5568;
    flex: 1;
}

.slide-body ul {
    list-style: none;
    padding: 0;
}

.slide-body li {
    margin: 15px 0;
    padding-left: 30px;
    position: relative;
}

.slide-body li:before {
    content: "•";
    color: #667eea;
    font-size: 1.5rem;
    position: absolute;
    left: 0;
    top: -2px;
}

/* 关键词标签 */
.keyword-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 20px;
    justify-content: center;
}

.keyword-tag {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
}

/* 图片幻灯片 */
.image-container {
    text-align: center;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.image-container img {
    max-width: 100%;
    max-height: 400px;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.image-caption {
    margin-top: 15px;
    font-style: italic;
    color: #718096;
}

/* 缩略图面板 */
.thumbnail-panel {
    width: 250px;
    background: white;
    border-left: 1px solid #e2e8f0;
    overflow-y: auto;
    padding: 15px;
}

.thumbnail-grid {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.thumbnail {
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    overflow: hidden;
}

.thumbnail:hover {
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.thumbnail.active {
    border-color: #667eea;
    background: #eef2ff;
}

.thumbnail-content {
    padding: 8px;
}

.thumbnail-slide {
    width: 100%;
    aspect-ratio: 16/9;
    background: white;
    border-radius: 4px;
    overflow: hidden;
    transform: scale(0.3);
    transform-origin: top left;
    width: 333.33%;
    height: 333.33%;
}

.thumbnail-info {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 8px;
    padding: 0 4px;
}

.thumbnail-number {
    background: #667eea;
    color: white;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: 600;
    flex-shrink: 0;
}

.thumbnail-title {
    font-size: 0.8rem;
    color: #4a5568;
    font-weight: 500;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 占位符 */
.slide-placeholder {
    text-align: center;
    color: #a0aec0;
    padding: 60px 20px;
}

.slide-placeholder i {
    font-size: 4rem;
    margin-bottom: 20px;
    color: #cbd5e0;
}

.slide-placeholder p {
    font-size: 1.2rem;
    margin: 0;
}

/* 备注区域 */
.slide-notes {
    background: white;
    border-top: 1px solid #e2e8f0;
    padding: 15px;
    max-height: 150px;
    overflow-y: auto;
}

.slide-notes h4 {
    margin: 0 0 10px 0;
    color: #4a5568;
    font-size: 1rem;
}

.notes-content {
    font-size: 0.9rem;
    line-height: 1.6;
    color: #718096;
}

/* 全屏模式 */
.ppt-viewer:fullscreen {
    background: black;
}

.ppt-viewer:fullscreen .slide-container {
    background: black;
    padding: 0;
}

.ppt-viewer:fullscreen .slide {
    max-width: 90vw;
    max-height: 90vh;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .ppt-toolbar {
        flex-direction: column;
        gap: 10px;
        padding: 15px;
    }
    
    .toolbar-left,
    .toolbar-center,
    .toolbar-right {
        justify-content: center;
    }
    
    .ppt-main {
        flex-direction: column;
    }
    
    .thumbnail-panel {
        width: 100%;
        height: 150px;
        border-left: none;
        border-top: 1px solid #e2e8f0;
    }
    
    .thumbnail-grid {
        flex-direction: row;
        overflow-x: auto;
        padding-bottom: 10px;
    }
    
    .thumbnail {
        min-width: 120px;
    }
    
    .slide-content {
        padding: 20px;
    }
    
    .slide-title {
        font-size: 1.8rem !important;
    }
    
    .slide-body {
        font-size: 1rem;
    }
}

/* 打印样式 */
@media print {
    .ppt-toolbar,
    .thumbnail-panel {
        display: none !important;
    }
    
    .slide {
        display: block !important;
        page-break-after: always;
        box-shadow: none;
        border: 1px solid #ccc;
    }
    
    .slide:last-child {
        page-break-after: avoid;
    }
}
