<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI PPT 生成器</title>
    
    <!-- 强制刷新CSS缓存 -->
    <link rel="stylesheet" href="styles.css?v=1.0">
    <link rel="stylesheet" href="ppt-viewer.css?v=1.0">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- 内联关键CSS确保基本样式 -->
    <style>
        /* 确保基本样式即使外部CSS未加载也能显示 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: white;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 20px;
        }
        
        .input-section, .preview-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .generate-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 20px;
        }
        
        .generate-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }
        
        #textInput {
            width: 100%;
            min-height: 200px;
            padding: 15px;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            font-size: 14px;
            line-height: 1.6;
            resize: vertical;
            transition: border-color 0.3s ease;
        }
        
        .hidden {
            display: none !important;
        }
        
        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <header class="header">
            <h1><i class="fas fa-magic"></i> AI PPT 生成器</h1>
            <p>智能分析您的内容，自动生成专业演示文稿</p>
        </header>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 输入区域 -->
            <section class="input-section">
                <div class="input-container">
                    <h2><i class="fas fa-edit"></i> 输入内容</h2>
                    <textarea 
                        id="textInput" 
                        placeholder="请输入您要制作PPT的内容...

例如：
公司年度总结报告
2023年业绩回顾

一、销售业绩
今年销售额达到1000万元，同比增长20%

二、市场拓展
新开拓了5个城市市场，客户满意度达到95%

三、未来规划
计划明年继续扩大市场份额，目标增长30%"
                        rows="10"
                    ></textarea>
                    
                    <div class="template-selection">
                        <div class="template-header">
                            <h3><i class="fas fa-palette"></i> 选择模板</h3>
                            <div class="template-actions">
                                <button id="reloadTemplatesBtn" class="action-btn" title="重新加载模板">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                                <button id="templateInfoBtn" class="action-btn" title="模板信息">
                                    <i class="fas fa-info-circle"></i>
                                </button>
                            </div>
                        </div>
                        <div class="template-grid" id="templateGrid">
                            <!-- 模板选项将通过JavaScript动态加载 -->
                        </div>
                        <div id="templateStats" class="template-stats hidden">
                            <!-- 模板统计信息 -->
                        </div>
                    </div>

                    <div class="options">
                        <h3><i class="fas fa-cog"></i> 生成选项</h3>
                        <div class="option-group">
                            <label>
                                <input type="checkbox" id="autoLayout" checked>
                                自动布局优化
                            </label>
                            <label>
                                <input type="checkbox" id="includeImages" checked>
                                包含图片建议
                            </label>
                            <label>
                                <input type="checkbox" id="addTransitions">
                                添加过渡效果
                            </label>
                        </div>
                    </div>

                    <button id="generateBtn" class="generate-btn">
                        <i class="fas fa-wand-magic-sparkles"></i>
                        生成 PPT
                    </button>
                </div>
            </section>

            <!-- 预览区域 -->
            <section class="preview-section">
                <div class="preview-container">
                    <h2><i class="fas fa-eye"></i> 预览</h2>
                    <div id="loadingIndicator" class="loading hidden">
                        <i class="fas fa-spinner fa-spin"></i>
                        <p>AI正在分析您的内容并生成PPT...</p>
                    </div>
                    
                    <div id="previewArea" class="preview-area">
                        <div class="preview-tabs">
                            <button class="tab-btn active" data-tab="simple">简单预览</button>
                            <button class="tab-btn" data-tab="ppt">PPT预览</button>
                        </div>
                        
                        <div id="simplePreview" class="tab-content active">
                            <div class="placeholder">
                                <i class="fas fa-presentation-screen"></i>
                                <p>生成的PPT预览将在这里显示</p>
                            </div>
                        </div>
                        
                        <div id="pptPreview" class="tab-content">
                            <!-- PPT预览器将在这里初始化 -->
                        </div>
                    </div>

                    <div id="slideNavigation" class="slide-navigation hidden">
                        <button id="prevSlide" class="nav-btn">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <span id="slideCounter">1 / 1</span>
                        <button id="nextSlide" class="nav-btn">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>

                    <div id="exportOptions" class="export-options hidden">
                        <h3><i class="fas fa-download"></i> 导出选项</h3>
                        <div class="export-buttons">
                            <button class="export-btn" data-format="pptx">
                                <i class="fas fa-file-powerpoint"></i>
                                PowerPoint
                            </button>
                            <button class="export-btn" data-format="pdf">
                                <i class="fas fa-file-pdf"></i>
                                PDF
                            </button>
                            <button class="export-btn" data-format="html">
                                <i class="fas fa-globe"></i>
                                网页版
                            </button>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <!-- 状态栏 -->
        <div id="statusBar" class="status-bar">
            <span id="statusText">就绪</span>
        </div>
    </div>

    <!-- 模态框 -->
    <div id="modal" class="modal hidden">
        <div class="modal-content">
            <span class="close">&times;</span>
            <div id="modalBody"></div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/axios/1.6.0/axios.min.js"></script>
    <script>
        // 检查CSS是否加载成功
        function checkCSSLoaded() {
            const testElement = document.createElement('div');
            testElement.className = 'template-card';
            testElement.style.display = 'none';
            document.body.appendChild(testElement);
            
            const computedStyle = window.getComputedStyle(testElement);
            const hasStyles = computedStyle.borderRadius !== '0px' || 
                             computedStyle.padding !== '0px';
            
            document.body.removeChild(testElement);
            
            if (!hasStyles) {
                console.warn('外部CSS未正确加载，使用内联样式');
                // 可以在这里添加更多内联样式作为备用
            }
            
            return hasStyles;
        }
        
        // 页面加载完成后检查CSS
        window.addEventListener('load', () => {
            setTimeout(checkCSSLoaded, 500);
        });
    </script>
    <script src="ppt-viewer.js?v=1.0"></script>
    <script src="app.js?v=1.0"></script>
</body>
</html>
