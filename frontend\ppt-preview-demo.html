<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PPT模板预览演示</title>
    <link rel="stylesheet" href="styles.css?v=1.0">
    <link rel="stylesheet" href="ppt-viewer.css?v=1.0">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 20px;
        }
        
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .demo-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .demo-header h1 {
            color: #2d3748;
            margin-bottom: 10px;
        }
        
        .demo-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #e2e8f0;
            border-radius: 10px;
            background: #f8fafc;
        }
        
        .template-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .template-item {
            background: white;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .template-item:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .template-item.selected {
            border-color: #667eea;
            background: #eef2ff;
        }
        
        .preview-container {
            height: 600px;
            border: 1px solid #e2e8f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
        }
        
        .status-info {
            background: #e6fffa;
            border: 1px solid #81e6d9;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .error-info {
            background: #fed7d7;
            border: 1px solid #fc8181;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        
        button {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            transition: all 0.3s ease;
        }
        
        button:hover {
            background: #5a67d8;
        }
        
        button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1><i class="fas fa-file-powerpoint"></i> PPT模板预览演示</h1>
            <p>测试真实PPT文件的在线预览功能</p>
        </div>

        <div class="demo-section">
            <h2><i class="fas fa-list"></i> 可用模板</h2>
            <div id="templateList" class="template-list">
                <!-- 模板列表将在这里显示 -->
            </div>
            <button onclick="loadTemplates()">🔄 刷新模板列表</button>
        </div>

        <div class="demo-section">
            <h2><i class="fas fa-eye"></i> 模板预览</h2>
            <div id="statusInfo" class="status-info">
                <strong>状态:</strong> <span id="statusText">请选择一个模板进行预览</span>
            </div>
            
            <div id="previewContainer" class="preview-container">
                <!-- PPT预览器将在这里初始化 -->
            </div>
            
            <div style="text-align: center; margin-top: 15px;">
                <button onclick="testAllTemplates()">🧪 测试所有模板</button>
                <button onclick="openMainApp()">🏠 返回主应用</button>
            </div>
        </div>

        <div class="demo-section">
            <h2><i class="fas fa-info-circle"></i> 功能说明</h2>
            <ul>
                <li><strong>真实PPT解析:</strong> 系统会尝试解析AiTemplate目录中的真实PPT文件</li>
                <li><strong>内容提取:</strong> 提取PPT中的文本、图片和结构信息</li>
                <li><strong>Web预览:</strong> 在浏览器中直接预览PPT内容</li>
                <li><strong>交互控制:</strong> 支持幻灯片导航、全屏播放等功能</li>
                <li><strong>备用方案:</strong> 如果解析失败，会显示模板基本信息</li>
            </ul>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/axios/1.6.0/axios.min.js"></script>
    <script src="ppt-viewer.js?v=1.0"></script>
    <script>
        let pptViewer = null;
        let templates = [];
        let selectedTemplateId = null;

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            initPPTViewer();
            loadTemplates();
        });

        function initPPTViewer() {
            pptViewer = new PPTViewer('previewContainer');
            console.log('PPT预览器初始化完成');
        }

        async function loadTemplates() {
            try {
                updateStatus('正在加载模板列表...', 'info');
                
                const response = await axios.get('/api/templates');
                
                if (response.data.success) {
                    templates = response.data.templates;
                    renderTemplateList();
                    updateStatus(`成功加载 ${templates.length} 个模板`, 'success');
                } else {
                    updateStatus('加载模板失败: ' + response.data.error, 'error');
                }
                
            } catch (error) {
                console.error('加载模板失败:', error);
                updateStatus('加载模板失败: ' + error.message, 'error');
            }
        }

        function renderTemplateList() {
            const templateList = document.getElementById('templateList');
            templateList.innerHTML = '';

            templates.forEach(template => {
                const templateItem = document.createElement('div');
                templateItem.className = 'template-item';
                templateItem.dataset.templateId = template.id;

                const icon = getTemplateIcon(template.category);
                const sizeText = template.size ? formatFileSize(template.size) : '';
                const isDefault = template.isDefault ? '(默认模板)' : '';

                templateItem.innerHTML = `
                    <div style="text-align: center;">
                        <i class="${icon}" style="font-size: 2rem; color: #667eea; margin-bottom: 10px;"></i>
                        <h3>${template.name} ${isDefault}</h3>
                        <p style="color: #718096; margin: 10px 0;">${template.description}</p>
                        ${template.filename ? `<small>文件: ${template.filename}</small><br>` : ''}
                        ${sizeText ? `<small>大小: ${sizeText}</small>` : ''}
                    </div>
                `;

                templateItem.addEventListener('click', () => {
                    selectTemplate(template.id);
                });

                templateList.appendChild(templateItem);
            });
        }

        function selectTemplate(templateId) {
            selectedTemplateId = templateId;
            
            // 更新UI
            document.querySelectorAll('.template-item').forEach(item => {
                item.classList.remove('selected');
            });
            
            document.querySelector(`[data-template-id="${templateId}"]`).classList.add('selected');
            
            // 加载模板预览
            loadTemplatePreview(templateId);
        }

        async function loadTemplatePreview(templateId) {
            try {
                updateStatus('正在加载模板预览...', 'info');
                
                const response = await axios.get(`/api/templates/${templateId}/preview`);
                
                if (response.data.success) {
                    const template = templates.find(t => t.id === templateId);
                    
                    if (pptViewer && response.data.slides) {
                        const pptData = {
                            slides: response.data.slides,
                            metadata: {
                                title: template.name,
                                author: 'PPT模板',
                                isTemplate: true,
                                isDefault: response.data.isDefault,
                                fileName: template.filename,
                                fileSize: template.size
                            }
                        };
                        
                        pptViewer.loadPPT(pptData);
                        
                        const slideCount = response.data.slides.length;
                        const method = response.data.extraction_method || '未知';
                        
                        updateStatus(
                            `模板预览加载成功！共 ${slideCount} 张幻灯片 (解析方式: ${method})`, 
                            'success'
                        );
                    } else {
                        updateStatus('模板预览加载失败：预览器未初始化', 'error');
                    }
                } else {
                    updateStatus(`模板预览加载失败: ${response.data.error}`, 'error');
                }
                
            } catch (error) {
                console.error('加载模板预览失败:', error);
                updateStatus('模板预览加载失败: ' + error.message, 'error');
            }
        }

        async function testAllTemplates() {
            updateStatus('开始测试所有模板...', 'info');
            
            for (let i = 0; i < templates.length; i++) {
                const template = templates[i];
                updateStatus(`测试模板 ${i + 1}/${templates.length}: ${template.name}`, 'info');
                
                try {
                    const response = await axios.get(`/api/templates/${template.id}/preview`);
                    console.log(`✅ 模板 ${template.name} 测试成功:`, response.data);
                } catch (error) {
                    console.error(`❌ 模板 ${template.name} 测试失败:`, error);
                }
                
                // 短暂延迟避免请求过快
                await new Promise(resolve => setTimeout(resolve, 500));
            }
            
            updateStatus('所有模板测试完成，请查看控制台日志', 'success');
        }

        function updateStatus(message, type = 'info') {
            const statusInfo = document.getElementById('statusInfo');
            const statusText = document.getElementById('statusText');
            
            statusText.textContent = message;
            
            // 更新样式
            statusInfo.className = type === 'error' ? 'error-info' : 'status-info';
            
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function getTemplateIcon(category) {
            const icons = {
                'business': 'fas fa-briefcase',
                'education': 'fas fa-graduation-cap',
                'creative': 'fas fa-palette',
                'general': 'fas fa-file-powerpoint'
            };
            return icons[category] || 'fas fa-file-powerpoint';
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function openMainApp() {
            window.open('/', '_blank');
        }
    </script>
</body>
</html>
