<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSS调试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .debug-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .status {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .success { background: #28a745; }
        .error { background: #dc3545; }
        .warning { background: #ffc107; }
        .info { background: #17a2b8; }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 12px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>🔍 CSS样式调试页面</h1>
        
        <div class="test-section">
            <h2>文件加载状态检查</h2>
            <div id="fileStatus">
                <p><span class="status info" id="htmlStatus"></span>HTML文件: 当前页面</p>
                <p><span class="status" id="cssMainStatus"></span>主样式文件: styles.css</p>
                <p><span class="status" id="cssPptStatus"></span>PPT样式文件: ppt-viewer.css</p>
                <p><span class="status" id="jsAppStatus"></span>应用脚本: app.js</p>
                <p><span class="status" id="jsPptStatus"></span>PPT脚本: ppt-viewer.js</p>
            </div>
            
            <button onclick="checkFiles()">🔄 重新检查文件</button>
            <button onclick="loadMainCSS()">📄 加载主样式</button>
            <button onclick="loadPPTCSS()">🎨 加载PPT样式</button>
        </div>

        <div class="test-section">
            <h2>网络请求测试</h2>
            <div id="networkStatus">
                <!-- 网络状态将在这里显示 -->
            </div>
            <button onclick="testNetworkRequests()">🌐 测试网络请求</button>
        </div>

        <div class="test-section">
            <h2>样式应用测试</h2>
            <div id="styleTest">
                <!-- 样式测试元素 -->
                <div class="test-element" style="display: none;">测试元素</div>
            </div>
            <button onclick="testStyleApplication()">🎨 测试样式应用</button>
        </div>

        <div class="test-section">
            <h2>控制台日志</h2>
            <pre id="consoleLog">等待日志...</pre>
            <button onclick="clearLog()">🗑️ 清空日志</button>
        </div>

        <div class="test-section">
            <h2>快速操作</h2>
            <button onclick="openMainApp()">🏠 打开主应用</button>
            <button onclick="openSimpleApp()">⚡ 打开简化版</button>
            <button onclick="reloadPage()">🔄 刷新页面</button>
        </div>
    </div>

    <script>
        let logMessages = [];

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
            logMessages.push(logEntry);
            updateConsoleLog();
            console.log(logEntry);
        }

        function updateConsoleLog() {
            document.getElementById('consoleLog').textContent = logMessages.slice(-20).join('\n');
        }

        function clearLog() {
            logMessages = [];
            updateConsoleLog();
        }

        async function checkFiles() {
            log('开始检查文件状态...');
            
            const files = [
                { id: 'cssMainStatus', url: '/styles.css', name: '主样式文件' },
                { id: 'cssPptStatus', url: '/ppt-viewer.css', name: 'PPT样式文件' },
                { id: 'jsAppStatus', url: '/app.js', name: '应用脚本' },
                { id: 'jsPptStatus', url: '/ppt-viewer.js', name: 'PPT脚本' }
            ];

            for (const file of files) {
                try {
                    const response = await fetch(file.url, { method: 'HEAD' });
                    const statusElement = document.getElementById(file.id);
                    
                    if (response.ok) {
                        statusElement.className = 'status success';
                        log(`${file.name} 加载成功 (${response.status})`);
                    } else {
                        statusElement.className = 'status error';
                        log(`${file.name} 加载失败 (${response.status})`, 'error');
                    }
                } catch (error) {
                    document.getElementById(file.id).className = 'status error';
                    log(`${file.name} 请求失败: ${error.message}`, 'error');
                }
            }
        }

        async function testNetworkRequests() {
            log('开始测试网络请求...');
            const networkDiv = document.getElementById('networkStatus');
            
            const tests = [
                { name: 'API健康检查', url: '/api/health' },
                { name: '模板列表', url: '/api/templates' },
                { name: '主页面', url: '/' },
                { name: '样式文件', url: '/styles.css' }
            ];

            let results = '<h4>网络请求结果:</h4>';
            
            for (const test of tests) {
                try {
                    const startTime = Date.now();
                    const response = await fetch(test.url);
                    const endTime = Date.now();
                    const duration = endTime - startTime;
                    
                    if (response.ok) {
                        results += `<p><span class="status success"></span>${test.name}: 成功 (${response.status}, ${duration}ms)</p>`;
                        log(`${test.name} 请求成功 (${duration}ms)`);
                    } else {
                        results += `<p><span class="status error"></span>${test.name}: 失败 (${response.status})</p>`;
                        log(`${test.name} 请求失败 (${response.status})`, 'error');
                    }
                } catch (error) {
                    results += `<p><span class="status error"></span>${test.name}: 错误 (${error.message})</p>`;
                    log(`${test.name} 请求错误: ${error.message}`, 'error');
                }
            }
            
            networkDiv.innerHTML = results;
        }

        function loadMainCSS() {
            log('尝试动态加载主样式文件...');
            
            // 移除现有的样式链接
            const existingLink = document.querySelector('link[href="styles.css"]');
            if (existingLink) {
                existingLink.remove();
                log('移除现有样式链接');
            }
            
            // 创建新的样式链接
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = '/styles.css?t=' + Date.now(); // 添加时间戳避免缓存
            
            link.onload = () => {
                log('主样式文件加载成功');
                document.getElementById('cssMainStatus').className = 'status success';
            };
            
            link.onerror = () => {
                log('主样式文件加载失败', 'error');
                document.getElementById('cssMainStatus').className = 'status error';
            };
            
            document.head.appendChild(link);
        }

        function loadPPTCSS() {
            log('尝试动态加载PPT样式文件...');
            
            const existingLink = document.querySelector('link[href*="ppt-viewer.css"]');
            if (existingLink) {
                existingLink.remove();
                log('移除现有PPT样式链接');
            }
            
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = '/ppt-viewer.css?t=' + Date.now();
            
            link.onload = () => {
                log('PPT样式文件加载成功');
                document.getElementById('cssPptStatus').className = 'status success';
            };
            
            link.onerror = () => {
                log('PPT样式文件加载失败', 'error');
                document.getElementById('cssPptStatus').className = 'status error';
            };
            
            document.head.appendChild(link);
        }

        function testStyleApplication() {
            log('测试样式应用...');
            const testDiv = document.getElementById('styleTest');
            
            // 创建测试元素
            testDiv.innerHTML = `
                <div class="generate-btn" style="margin: 10px 0; padding: 10px; display: inline-block;">
                    测试按钮样式
                </div>
                <div class="template-card" style="margin: 10px 0; padding: 10px; border: 1px solid #ccc; display: inline-block;">
                    测试卡片样式
                </div>
                <div class="ppt-viewer" style="margin: 10px 0; padding: 10px; background: #f0f0f0; display: inline-block;">
                    测试PPT预览器样式
                </div>
            `;
            
            // 检查样式是否应用
            setTimeout(() => {
                const elements = testDiv.querySelectorAll('div');
                elements.forEach((el, index) => {
                    const computedStyle = window.getComputedStyle(el);
                    const hasCustomStyles = computedStyle.backgroundColor !== 'rgba(0, 0, 0, 0)' || 
                                           computedStyle.borderRadius !== '0px' ||
                                           computedStyle.boxShadow !== 'none';
                    
                    log(`测试元素 ${index + 1}: ${hasCustomStyles ? '样式已应用' : '样式未应用'}`);
                });
            }, 100);
        }

        function openMainApp() {
            window.open('/', '_blank');
        }

        function openSimpleApp() {
            window.open('/simple.html', '_blank');
        }

        function reloadPage() {
            window.location.reload();
        }

        // 页面加载时自动检查
        window.addEventListener('load', () => {
            log('页面加载完成，开始自动检查...');
            document.getElementById('htmlStatus').className = 'status success';
            setTimeout(checkFiles, 500);
        });

        // 捕获所有错误
        window.addEventListener('error', (event) => {
            log(`JavaScript错误: ${event.message} (${event.filename}:${event.lineno})`, 'error');
        });

        // 捕获资源加载错误
        window.addEventListener('error', (event) => {
            if (event.target !== window) {
                log(`资源加载错误: ${event.target.src || event.target.href}`, 'error');
            }
        }, true);
    </script>
</body>
</html>
