<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI PPT Generator - 快速测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 3px;
            white-space: pre-wrap;
        }
        .success {
            border-left: 4px solid #28a745;
        }
        .error {
            border-left: 4px solid #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 AI PPT Generator 快速测试</h1>
        
        <div class="test-section">
            <h3>1. 健康检查</h3>
            <button onclick="testHealth()">测试 API 健康状态</button>
            <div id="healthResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>2. 获取模板</h3>
            <button onclick="testTemplates()">获取可用模板</button>
            <div id="templatesResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>3. 生成 PPT</h3>
            <button onclick="testGenerate()">生成示例 PPT</button>
            <div id="generateResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>4. 完整界面</h3>
            <button onclick="openFullApp()">打开完整应用界面</button>
            <p>点击上方按钮将在新标签页中打开完整的 AI PPT Generator 界面</p>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3000/api';

        async function testHealth() {
            const resultDiv = document.getElementById('healthResult');
            resultDiv.textContent = '正在测试...';
            
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                
                resultDiv.className = 'result success';
                resultDiv.textContent = `✅ 成功！\n${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 失败：${error.message}`;
            }
        }

        async function testTemplates() {
            const resultDiv = document.getElementById('templatesResult');
            resultDiv.textContent = '正在获取模板...';
            
            try {
                const response = await fetch(`${API_BASE}/templates`);
                const data = await response.json();
                
                resultDiv.className = 'result success';
                resultDiv.textContent = `✅ 成功获取 ${data.templates.length} 个模板：\n${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 失败：${error.message}`;
            }
        }

        async function testGenerate() {
            const resultDiv = document.getElementById('generateResult');
            resultDiv.textContent = '正在生成 PPT...';
            
            const testData = {
                text: `公司年度总结报告
2023年业绩回顾

一、销售业绩
今年销售额达到1000万元，同比增长20%

二、市场拓展
新开拓了5个城市市场，客户满意度达到95%

三、未来规划
计划明年继续扩大市场份额，目标增长30%`,
                templateId: 1,
                options: {
                    autoLayout: true,
                    includeImages: true,
                    addTransitions: false
                }
            };
            
            try {
                const response = await fetch(`${API_BASE}/generate-ppt`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testData)
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ PPT 生成成功！
幻灯片数量：${data.slides.length}
模板：${data.metadata.template.name}
生成时间：${data.metadata.generatedAt}

幻灯片预览：
${data.slides.map((slide, index) => 
    `${index + 1}. ${slide.title} (${slide.type})`
).join('\n')}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 生成失败：${data.error}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 失败：${error.message}`;
            }
        }

        function openFullApp() {
            window.open('http://localhost:3000', '_blank');
        }

        // 页面加载时自动测试健康状态
        window.onload = function() {
            testHealth();
        };
    </script>
</body>
</html>
