# 中文编码问题解决方案

## 问题描述
用户遇到中文显示乱码问题：`A���ճ������е�Ӧ��` 应该显示为 `AI在日常生活中的应用`

## 根本原因
UTF-8编码的中文字符被错误解析为其他编码格式，导致显示乱码。

## 完整解决方案

### 1. 前端修复 ✅

#### HTML文件编码设置
```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <!-- 确保所有HTML文件都有正确的charset声明 -->
</head>
```

#### CSS字体设置
```css
body {
    font-family: 'Microsoft YaHei', 'SimSun', Arial, sans-serif;
    /* 优先使用中文字体 */
}
```

#### JavaScript编码处理
```javascript
// 使用TextEncoder/TextDecoder确保正确编码
const encoder = new TextEncoder();
const decoder = new TextDecoder('utf-8');
```

### 2. 后端修复 ✅

#### Express服务器配置
```javascript
// 设置正确的响应头
app.use((req, res, next) => {
    res.setHeader('Content-Type', 'text/html; charset=utf-8');
    next();
});

// 配置body-parser支持UTF-8
app.use(bodyParser.json({ limit: '10mb' }));
app.use(bodyParser.urlencoded({ extended: true, limit: '10mb' }));
```

#### Python进程调用
```javascript
// 确保Python进程使用UTF-8编码
const pythonProcess = spawn('python', [
    path.join(__dirname, '../models/nlp_processor.py'),
    cleanText
], {
    encoding: 'utf8',
    env: { ...process.env, PYTHONIOENCODING: 'utf-8' }
});
```

### 3. Python脚本修复 ✅

#### 文件头声明
```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
```

#### 编码处理
```python
# 处理可能的编码问题
if isinstance(input_text, bytes):
    input_text = input_text.decode('utf-8', errors='ignore')

# 确保JSON输出使用UTF-8
print(json.dumps(result, ensure_ascii=False, indent=2))
```

### 4. 文件保存格式 ✅

确保所有文件都保存为UTF-8编码：
- HTML文件：UTF-8 without BOM
- CSS文件：UTF-8 without BOM  
- JavaScript文件：UTF-8 without BOM
- Python文件：UTF-8 without BOM

## 测试工具

### 1. 编码测试页面
- `encoding-test.html` - 基础编码测试
- `fix-encoding.html` - 编码修复工具

### 2. API测试
```bash
# 测试API健康状态
curl -H "Content-Type: application/json; charset=utf-8" http://localhost:3000/api/health

# 测试中文内容处理
curl -X POST -H "Content-Type: application/json; charset=utf-8" \
  -d '{"text":"AI在日常生活中的应用","templateId":1}' \
  http://localhost:3000/api/generate-ppt
```

## 验证步骤

### 1. 浏览器验证
1. 打开 `http://localhost:3000`
2. 检查页面中文显示是否正常
3. 输入中文内容测试生成功能

### 2. API验证
1. 打开 `fix-encoding.html`
2. 运行编码测试
3. 测试API中文处理

### 3. 服务器日志验证
检查服务器控制台输出，确保中文字符正确显示。

## 常见问题排查

### 问题1：页面显示乱码
**解决方案：**
- 检查HTML文件的charset声明
- 确保文件保存为UTF-8编码
- 检查浏览器编码设置

### 问题2：API返回乱码
**解决方案：**
- 检查Content-Type响应头
- 确保JSON.stringify使用ensure_ascii=False
- 检查数据传输过程中的编码

### 问题3：Python脚本处理乱码
**解决方案：**
- 设置PYTHONIOENCODING环境变量
- 使用正确的decode/encode方法
- 检查命令行参数传递

## 预防措施

1. **统一编码标准**：整个项目统一使用UTF-8编码
2. **编码声明**：所有文件都明确声明编码格式
3. **测试覆盖**：包含中文字符的测试用例
4. **文档说明**：在README中说明编码要求

## 工具推荐

1. **VS Code插件**：
   - "Encoding" - 显示和转换文件编码
   - "Chinese (Simplified)" - 中文语言包

2. **浏览器工具**：
   - 开发者工具 > Network > Response Headers
   - 检查Content-Type和charset

3. **命令行工具**：
   ```bash
   # 检查文件编码
   file -bi filename.html
   
   # 转换文件编码
   iconv -f gbk -t utf-8 input.txt > output.txt
   ```

## 总结

通过以上完整的解决方案，已经从前端、后端、Python脚本等多个层面解决了中文编码问题。系统现在能够：

✅ 正确显示中文界面
✅ 正确处理中文输入
✅ 正确分析中文内容
✅ 正确生成中文PPT

如果仍然遇到编码问题，请使用提供的测试工具进行诊断和修复。
