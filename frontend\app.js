// AI PPT Generator - 前端JavaScript

class AIPPTGenerator {
    constructor() {
        this.currentSlideIndex = 0;
        this.slides = [];
        this.selectedTemplateId = 1;
        this.pptViewer = null;
        this.currentTab = 'simple';
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadTemplates();
        this.initPPTViewer();
        this.updateStatus('就绪');
    }

    initPPTViewer() {
        // 初始化PPT预览器
        this.pptViewer = new PPTViewer('pptPreview');
    }

    bindEvents() {
        // 生成按钮事件
        document.getElementById('generateBtn').addEventListener('click', () => {
            this.generatePPT();
        });

        // 幻灯片导航事件
        document.getElementById('prevSlide').addEventListener('click', () => {
            this.previousSlide();
        });

        document.getElementById('nextSlide').addEventListener('click', () => {
            this.nextSlide();
        });

        // 导出按钮事件
        document.querySelectorAll('.export-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const format = e.target.closest('.export-btn').dataset.format;
                this.exportPPT(format);
            });
        });

        // 文本输入事件
        document.getElementById('textInput').addEventListener('input', () => {
            this.updateWordCount();
        });

        // 模板管理事件
        document.getElementById('reloadTemplatesBtn').addEventListener('click', () => {
            this.reloadTemplates();
        });

        document.getElementById('templateInfoBtn').addEventListener('click', () => {
            this.toggleTemplateStats();
        });

        // 预览标签页事件
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });
    }

    async loadTemplates() {
        try {
            const response = await axios.get('/api/templates');
            if (response.data.success) {
                this.templates = response.data.templates;
                this.templateStats = response.data.stats;
                this.renderTemplates(response.data.templates);
                this.updateStatus(`成功加载 ${response.data.templates.length} 个模板`);
            }
        } catch (error) {
            console.error('加载模板失败:', error);
            this.showError('加载模板失败，请刷新页面重试');
        }
    }

    async reloadTemplates() {
        try {
            this.updateStatus('正在重新加载模板...');
            const response = await axios.post('/api/templates/reload');
            if (response.data.success) {
                this.templates = response.data.templates;
                this.renderTemplates(response.data.templates);
                this.updateStatus(response.data.message);
            }
        } catch (error) {
            console.error('重新加载模板失败:', error);
            this.showError('重新加载模板失败');
        }
    }

    toggleTemplateStats() {
        const statsDiv = document.getElementById('templateStats');
        if (statsDiv.classList.contains('hidden')) {
            this.renderTemplateStats();
            statsDiv.classList.remove('hidden');
        } else {
            statsDiv.classList.add('hidden');
        }
    }

    renderTemplateStats() {
        const statsDiv = document.getElementById('templateStats');
        if (!this.templateStats) return;

        const categoryStats = Object.entries(this.templateStats.categories)
            .map(([category, count]) => `
                <div class="stat-item">
                    <div class="stat-value">${count}</div>
                    <div class="stat-label">${this.getCategoryName(category)}</div>
                </div>
            `).join('');

        statsDiv.innerHTML = `
            <h4><i class="fas fa-chart-bar"></i> 模板统计</h4>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-value">${this.templateStats.total}</div>
                    <div class="stat-label">总数</div>
                </div>
                ${categoryStats}
                <div class="stat-item">
                    <div class="stat-value">${this.formatFileSize(this.templateStats.totalSize)}</div>
                    <div class="stat-label">总大小</div>
                </div>
            </div>
        `;
    }

    getCategoryName(category) {
        const names = {
            'business': '商务',
            'education': '教育',
            'creative': '创意',
            'general': '通用'
        };
        return names[category] || category;
    }

    switchTab(tabName) {
        // 更新标签按钮状态
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.tab === tabName);
        });

        // 更新内容区域
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.toggle('active', content.id === `${tabName}Preview`);
        });

        this.currentTab = tabName;

        // 如果切换到PPT预览且有幻灯片数据，重新加载
        if (tabName === 'ppt' && this.slides.length > 0) {
            this.renderPPTPreview();
        }
    }



    renderTemplates(templates) {
        const templateGrid = document.getElementById('templateGrid');
        templateGrid.innerHTML = '';

        templates.forEach(template => {
            const templateCard = document.createElement('div');
            templateCard.className = `template-card ${template.id === this.selectedTemplateId ? 'selected' : ''}`;
            templateCard.dataset.templateId = template.id;

            const icon = this.getTemplateIcon(template.category);
            const sizeText = template.size ? this.formatFileSize(template.size) : '';
            const isDefault = template.isDefault ? '(默认)' : '';

            templateCard.innerHTML = `
                <i class="${icon}"></i>
                <h4>${template.name} ${isDefault}</h4>
                <p>${template.description}</p>
                ${template.filename ? `<small>文件: ${template.filename}</small>` : ''}
                ${sizeText ? `<small>大小: ${sizeText}</small>` : ''}
            `;

            templateCard.addEventListener('click', () => {
                this.selectTemplate(template.id);
            });

            templateGrid.appendChild(templateCard);
        });
    }

    getTemplateIcon(category) {
        const icons = {
            'business': 'fas fa-briefcase',
            'education': 'fas fa-graduation-cap',
            'creative': 'fas fa-palette',
            'general': 'fas fa-file-powerpoint'
        };
        return icons[category] || 'fas fa-file-powerpoint';
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    selectTemplate(templateId) {
        this.selectedTemplateId = templateId;

        // 更新UI
        document.querySelectorAll('.template-card').forEach(card => {
            card.classList.remove('selected');
        });

        document.querySelector(`[data-template-id="${templateId}"]`).classList.add('selected');
        this.updateStatus(`已选择模板 ID: ${templateId}`);

        // 加载模板预览
        this.loadTemplatePreview(templateId);
    }

    async loadTemplatePreview(templateId) {
        try {
            this.updateStatus('正在加载模板预览...');

            const response = await axios.get(`/api/templates/${templateId}/preview`);

            if (response.data.success) {
                // 在PPT预览器中显示模板内容
                if (this.pptViewer && response.data.slides) {
                    const pptData = {
                        slides: response.data.slides,
                        metadata: {
                            title: response.data.template.name,
                            author: 'PPT模板',
                            isTemplate: true,
                            isDefault: response.data.isDefault
                        }
                    };

                    this.pptViewer.loadPPT(pptData);

                    // 切换到PPT预览标签
                    this.switchTab('ppt');

                    this.updateStatus(`模板预览加载成功 - ${response.data.template.name}`);
                } else {
                    this.updateStatus('模板预览加载失败：预览器未初始化');
                }
            } else {
                this.updateStatus(`模板预览加载失败: ${response.data.error}`);
            }

        } catch (error) {
            console.error('加载模板预览失败:', error);
            this.updateStatus('模板预览加载失败，请稍后重试');
        }
    }

    async generatePPT() {
        const textInput = document.getElementById('textInput');
        const text = textInput.value.trim();

        if (!text) {
            this.showError('请输入要生成PPT的内容');
            return;
        }

        // 获取选项
        const options = {
            autoLayout: document.getElementById('autoLayout').checked,
            includeImages: document.getElementById('includeImages').checked,
            addTransitions: document.getElementById('addTransitions').checked
        };

        try {
            this.showLoading(true);
            this.updateStatus('正在生成PPT...');

            const response = await axios.post('/api/generate-ppt', {
                text: text,
                templateId: this.selectedTemplateId,
                options: options
            });

            if (response.data.success) {
                this.slides = response.data.slides;
                this.renderSlides();
                this.showExportOptions(true);
                this.updateStatus(`PPT生成成功！共 ${this.slides.length} 张幻灯片`);
            } else {
                this.showError(response.data.error || '生成PPT失败');
            }

        } catch (error) {
            console.error('生成PPT错误:', error);
            this.showError('生成PPT时发生错误，请检查网络连接后重试');
        } finally {
            this.showLoading(false);
        }
    }

    renderSlides() {
        // 渲染简单预览
        this.renderSimplePreview();

        // 渲染PPT预览
        this.renderPPTPreview();

        this.currentSlideIndex = 0;
        this.updateSlideNavigation();
        this.showSlideNavigation(true);
    }

    renderSimplePreview() {
        const simplePreview = document.getElementById('simplePreview');
        simplePreview.innerHTML = '';

        this.slides.forEach((slide, index) => {
            const slideElement = document.createElement('div');
            slideElement.className = `slide ${slide.type}-slide ${index === 0 ? 'active' : ''}`;
            slideElement.dataset.slideIndex = index;

            slideElement.innerHTML = this.generateSlideHTML(slide);
            simplePreview.appendChild(slideElement);
        });
    }

    renderPPTPreview() {
        if (this.pptViewer && this.slides.length > 0) {
            // 为PPT预览器准备数据
            const pptData = {
                slides: this.slides.map((slide, index) => ({
                    ...slide,
                    id: index + 1,
                    notes: slide.notes || '',
                    image: slide.image || null
                })),
                metadata: {
                    title: this.slides[0]?.title || 'AI生成的PPT',
                    author: 'AI PPT Generator',
                    createdAt: new Date().toISOString()
                }
            };

            this.pptViewer.loadPPT(pptData);
        }
    }

    generateSlideHTML(slide) {
        switch (slide.type) {
            case 'title':
                return `
                    <h1>${slide.title}</h1>
                    <h2>${slide.subtitle}</h2>
                `;

            case 'content':
                const keywordsHTML = slide.keywords && slide.keywords.length > 0
                    ? `<div class="keywords">
                         ${slide.keywords.map(keyword => `<span class="keyword-tag">${keyword}</span>`).join('')}
                       </div>`
                    : '';

                return `
                    <h1>${slide.title}</h1>
                    <div class="content">${this.formatContent(slide.content)}</div>
                    ${keywordsHTML}
                `;

            case 'summary':
                return `
                    <h1>${slide.title}</h1>
                    <div class="content">${this.formatContent(slide.content)}</div>
                `;

            default:
                return `<div class="content">${slide.content || ''}</div>`;
        }
    }

    formatContent(content) {
        if (!content) return '';

        // 将换行符转换为HTML换行
        return content.replace(/\n/g, '<br>');
    }

    previousSlide() {
        if (this.currentSlideIndex > 0) {
            this.currentSlideIndex--;
            this.updateSlideDisplay();
        }
    }

    nextSlide() {
        if (this.currentSlideIndex < this.slides.length - 1) {
            this.currentSlideIndex++;
            this.updateSlideDisplay();
        }
    }

    updateSlideDisplay() {
        // 隐藏所有幻灯片
        document.querySelectorAll('.slide').forEach(slide => {
            slide.classList.remove('active');
        });

        // 显示当前幻灯片
        const currentSlide = document.querySelector(`[data-slide-index="${this.currentSlideIndex}"]`);
        if (currentSlide) {
            currentSlide.classList.add('active');
        }

        this.updateSlideNavigation();
    }

    updateSlideNavigation() {
        const prevBtn = document.getElementById('prevSlide');
        const nextBtn = document.getElementById('nextSlide');
        const counter = document.getElementById('slideCounter');

        prevBtn.disabled = this.currentSlideIndex === 0;
        nextBtn.disabled = this.currentSlideIndex === this.slides.length - 1;
        counter.textContent = `${this.currentSlideIndex + 1} / ${this.slides.length}`;
    }

    async exportPPT(format) {
        if (!this.slides || this.slides.length === 0) {
            this.showError('请先生成PPT内容再进行导出');
            return;
        }

        try {
            this.updateStatus(`正在导出为 ${format.toUpperCase()} 格式...`);

            // 准备导出数据
            const exportData = {
                slides: this.slides,
                metadata: {
                    title: this.slides[0]?.title || 'AI生成的PPT',
                    author: 'AI PPT Generator',
                    subject: 'AI生成的演示文稿',
                    createdAt: new Date().toISOString()
                },
                format: format
            };

            // 调用导出API
            const response = await axios.post('/api/export-ppt', exportData, {
                timeout: 30000  // 30秒超时
            });

            if (response.data.success) {
                const file = response.data.file;
                this.updateStatus(`导出成功！文件大小: ${this.formatFileSize(file.size)}`);

                // 自动下载文件
                this.downloadFile(file.downloadUrl, file.name);

                // 显示成功消息
                alert(`✅ PPT导出成功！\n\n文件名: ${file.name}\n文件大小: ${this.formatFileSize(file.size)}\n幻灯片数量: ${file.slideCount}\n\n文件将自动下载...`);

            } else {
                this.showError(`导出失败: ${response.data.error}`);
            }

        } catch (error) {
            console.error('PPT导出错误:', error);
            if (error.code === 'ECONNABORTED') {
                this.showError('导出超时，请稍后重试');
            } else {
                this.showError(`导出失败: ${error.message}`);
            }
        }
    }

    // 下载文件
    downloadFile(url, filename) {
        try {
            // 创建隐藏的下载链接
            const link = document.createElement('a');
            link.href = url;
            link.download = filename;
            link.style.display = 'none';

            // 添加到页面并触发点击
            document.body.appendChild(link);
            link.click();

            // 清理
            setTimeout(() => {
                document.body.removeChild(link);
            }, 100);

            console.log('文件下载已触发:', filename);

        } catch (error) {
            console.error('文件下载失败:', error);
            this.showError('文件下载失败，请手动访问下载链接');
        }
    }

    updateWordCount() {
        const text = document.getElementById('textInput').value;
        const wordCount = text.trim().split(/\s+/).length;
        const charCount = text.length;

        if (text.trim()) {
            this.updateStatus(`字数: ${wordCount} | 字符数: ${charCount}`);
        } else {
            this.updateStatus('就绪');
        }
    }

    showLoading(show) {
        const loadingIndicator = document.getElementById('loadingIndicator');
        const previewArea = document.getElementById('previewArea');

        if (show) {
            loadingIndicator.classList.remove('hidden');
            previewArea.style.display = 'none';
        } else {
            loadingIndicator.classList.add('hidden');
            previewArea.style.display = 'flex';
        }
    }

    showSlideNavigation(show) {
        const navigation = document.getElementById('slideNavigation');
        if (show) {
            navigation.classList.remove('hidden');
        } else {
            navigation.classList.add('hidden');
        }
    }

    showExportOptions(show) {
        const exportOptions = document.getElementById('exportOptions');
        if (show) {
            exportOptions.classList.remove('hidden');
        } else {
            exportOptions.classList.add('hidden');
        }
    }

    updateStatus(message) {
        document.getElementById('statusText').textContent = message;
    }

    showError(message) {
        this.updateStatus(`错误: ${message}`);
        alert(message);
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new AIPPTGenerator();
});
