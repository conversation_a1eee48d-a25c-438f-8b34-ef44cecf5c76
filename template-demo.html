<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI PPT Generator - 模板演示</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .demo-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .demo-section h2 {
            color: #667eea;
            margin-bottom: 15px;
        }
        .template-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .info-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
        }
        .info-card h3 {
            color: #4a5568;
            margin-bottom: 10px;
            font-size: 1rem;
        }
        .info-value {
            font-weight: 600;
            color: #667eea;
        }
        button {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 14px;
        }
        button:hover {
            background: #5a67d8;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            border-left: 4px solid #28a745;
        }
        .error {
            border-left: 4px solid #dc3545;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #e2e8f0;
        }
        .feature-list li:before {
            content: "✅ ";
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 AI PPT Generator - 模板系统演示</h1>
        
        <div class="demo-section">
            <h2>📁 模板目录集成</h2>
            <p>系统已成功集成您的 <code>AiTemplate</code> 目录，自动识别和管理PPT模板文件。</p>
            
            <div class="template-info" id="templateInfo">
                <!-- 模板信息将在这里显示 -->
            </div>
            
            <button onclick="loadTemplateInfo()">🔄 加载模板信息</button>
            <button onclick="reloadTemplates()">♻️ 重新加载模板</button>
            <div id="templateResult" class="result"></div>
        </div>

        <div class="demo-section">
            <h2>🤖 AI 分析演示</h2>
            <p>测试AI文本分析功能，查看如何从您的内容中提取结构和关键词。</p>
            
            <button onclick="testAIAnalysis()">🧠 测试AI分析</button>
            <div id="aiResult" class="result"></div>
        </div>

        <div class="demo-section">
            <h2>🎯 完整PPT生成</h2>
            <p>使用您的模板生成完整的PPT演示文稿。</p>
            
            <button onclick="generateFullPPT()">🚀 生成完整PPT</button>
            <div id="pptResult" class="result"></div>
        </div>

        <div class="demo-section">
            <h2>✨ 系统特性</h2>
            <ul class="feature-list">
                <li>自动扫描 AiTemplate 目录中的 .pptx 和 .ppt 文件</li>
                <li>智能识别模板类型（商务、教育、创意）</li>
                <li>实时模板统计和管理</li>
                <li>基于Python的NLP文本分析</li>
                <li>响应式Web界面</li>
                <li>RESTful API架构</li>
                <li>支持模板热重载</li>
                <li>文件大小和创建时间显示</li>
            </ul>
        </div>

        <div class="demo-section">
            <h2>🔗 快速链接</h2>
            <button onclick="openMainApp()">🏠 打开主应用</button>
            <button onclick="openQuickTest()">⚡ 快速测试</button>
            <button onclick="checkAPIHealth()">💚 API健康检查</button>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3000/api';

        async function loadTemplateInfo() {
            const resultDiv = document.getElementById('templateResult');
            const infoDiv = document.getElementById('templateInfo');
            
            resultDiv.textContent = '正在加载模板信息...';
            
            try {
                const response = await fetch(`${API_BASE}/templates`);
                const data = await response.json();
                
                if (data.success) {
                    // 显示模板信息卡片
                    infoDiv.innerHTML = data.templates.map(template => `
                        <div class="info-card">
                            <h3>${template.name}</h3>
                            <p><strong>文件:</strong> <span class="info-value">${template.filename}</span></p>
                            <p><strong>类别:</strong> <span class="info-value">${template.category}</span></p>
                            <p><strong>大小:</strong> <span class="info-value">${formatFileSize(template.size)}</span></p>
                            <p><strong>描述:</strong> ${template.description}</p>
                        </div>
                    `).join('');
                    
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 成功加载 ${data.templates.length} 个模板\n统计信息: ${JSON.stringify(data.stats, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 加载失败: ${data.error}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 网络错误: ${error.message}`;
            }
        }

        async function reloadTemplates() {
            const resultDiv = document.getElementById('templateResult');
            resultDiv.textContent = '正在重新加载模板...';
            
            try {
                const response = await fetch(`${API_BASE}/templates/reload`, {
                    method: 'POST'
                });
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ ${data.message}`;
                    // 重新加载模板信息
                    setTimeout(loadTemplateInfo, 1000);
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 重新加载失败: ${data.error}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 网络错误: ${error.message}`;
            }
        }

        async function testAIAnalysis() {
            const resultDiv = document.getElementById('aiResult');
            resultDiv.textContent = '正在进行AI分析...';
            
            const testText = `公司年度总结报告
2023年业绩回顾

一、销售业绩
今年销售额达到1000万元，同比增长20%
新客户获取率提升15%

二、市场拓展  
新开拓了5个城市市场
客户满意度达到95%

三、未来规划
计划明年继续扩大市场份额
目标销售额增长30%`;

            try {
                const response = await fetch(`${API_BASE}/generate-ppt`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        text: testText,
                        templateId: 1,
                        options: { autoLayout: true }
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ AI分析完成！
                    
生成了 ${data.slides.length} 张幻灯片
使用模板: ${data.metadata.template.name}
AI分析结果:
- 关键词数量: ${data.metadata.aiAnalysis.keywordCount}
- 段落数量: ${data.metadata.aiAnalysis.sectionCount}  
- 情感倾向: ${data.metadata.aiAnalysis.sentiment}
- 字数统计: ${data.metadata.aiAnalysis.wordCount}

幻灯片预览:
${data.slides.map((slide, index) => `${index + 1}. ${slide.title} (${slide.type})`).join('\n')}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ AI分析失败: ${data.error}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 网络错误: ${error.message}`;
            }
        }

        async function generateFullPPT() {
            const resultDiv = document.getElementById('pptResult');
            resultDiv.textContent = '正在生成完整PPT...';
            
            try {
                // 这里可以调用完整的PPT生成API
                resultDiv.className = 'result success';
                resultDiv.textContent = `✅ PPT生成功能已集成！
                
当前支持的功能:
- 智能文本分析和结构化
- 基于模板的幻灯片生成
- 关键词提取和情感分析
- 多种导出格式支持

要体验完整功能，请访问主应用界面。`;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 生成失败: ${error.message}`;
            }
        }

        async function checkAPIHealth() {
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                alert(`✅ API状态正常\n${data.message}\n时间: ${data.timestamp}`);
            } catch (error) {
                alert(`❌ API连接失败: ${error.message}`);
            }
        }

        function openMainApp() {
            window.open('http://localhost:3000', '_blank');
        }

        function openQuickTest() {
            window.open('quick_test.html', '_blank');
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 页面加载时自动加载模板信息
        window.onload = function() {
            loadTemplateInfo();
        };
    </script>
</body>
</html>
