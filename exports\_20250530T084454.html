<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>目录页</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #000;
            color: #fff;
            overflow: hidden;
        }
        .presentation {
            width: 100vw;
            height: 100vh;
            position: relative;
        }
        .slide {
            width: 100%;
            height: 100%;
            display: none;
            padding: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            position: absolute;
            top: 0;
            left: 0;
        }
        .slide.active { display: flex; flex-direction: column; justify-content: center; }
        .slide.title-slide { text-align: center; }
        .slide h1 {
            font-size: 3rem;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .slide h2 {
            font-size: 2rem;
            margin-bottom: 20px;
            color: #f0f8ff;
        }
        .slide .content {
            font-size: 1.5rem;
            line-height: 1.8;
            margin: 20px 0;
        }
        .slide .keywords {
            margin-top: 30px;
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            justify-content: center;
        }
        .keyword-tag {
            background: rgba(255,255,255,0.2);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 1rem;
        }
        .controls {
            position: fixed;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1000;
        }
        .controls button {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            padding: 10px 20px;
            margin: 0 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1rem;
        }
        .controls button:hover { background: rgba(255,255,255,0.3); }
        .slide-counter {
            background: rgba(0,0,0,0.5);
            padding: 5px 15px;
            border-radius: 15px;
            margin: 0 10px;
        }
        .print-mode .slide {
            display: block !important;
            page-break-after: always;
            position: static;
            height: auto;
            min-height: 100vh;
        }
        @media print {
            .controls { display: none; }
            .slide {
                display: block !important;
                page-break-after: always;
                position: static;
                height: auto;
                min-height: 100vh;
            }
        }
    </style>
</head>
<body>
    <div class="presentation">
        
        <div class="slide title-slide title-slide">
            <h1>目录页</h1>
            <h2>基于AI生成</h2>
            
        </div>

        <div class="slide content-slide">
            <h1>基础穿衣原则</h1>
            <div class="content">场合着装指南<br>季节与气候搭配<br>体型适配技巧<br>色彩搭配法则<br>必备单品推荐<br>品牌与预算选择<br>常见错误与解决方案</div>
            <div class="keywords"><span class="keyword-tag">场合着装指南</span><span class="keyword-tag">季节与气候搭配</span><span class="keyword-tag">体型适配技巧</span><span class="keyword-tag">色彩搭配法则</span><span class="keyword-tag">必备单品推荐</span></div>
        </div>

        <div class="slide content-slide">
            <h1>1. 基础穿衣原则</h1>
            <div class="content">3大核心原则<br>合身：肩线、袖长、裤脚的关键细节<br>简洁：Less is More（避免过多装饰）<br>场合适配：商务/休闲/运动场景区分<br>示例图片：合身与不合身对比图</div>
            <div class="keywords"><span class="keyword-tag">3大核心原则</span><span class="keyword-tag">合身肩线袖长裤脚的关键细节</span><span class="keyword-tag">简洁Less</span><span class="keyword-tag">is</span><span class="keyword-tag">More避免过多装饰</span></div>
        </div>

        <div class="slide content-slide">
            <h1>2. 场合着装指南</h1>
            <div class="content">商务正装<br>搭配：深色西装+纯色衬衫+牛津鞋<br>细节：领带宽度与西装领比例<br>商务休闲<br>搭配：休闲西装+针织衫+乐福鞋<br>休闲约会<br>推荐：牛仔夹克+白T恤+小白鞋<br>宴会派对<br>选择：丝绒西装+领结+雕花皮鞋</div>
            <div class="keywords"><span class="keyword-tag">商务正装</span><span class="keyword-tag">搭配深色西装纯色衬衫牛津鞋</span><span class="keyword-tag">细节领带宽度与西装领比例</span><span class="keyword-tag">商务休闲</span><span class="keyword-tag">搭配休闲西装针织衫乐福鞋</span></div>
        </div>

        <div class="slide content-slide">
            <h1>3. 季节与气候搭配</h1>
            <div class="content">春夏<br>面料：亚麻、棉质<br>颜色：浅蓝、米白、卡其<br>秋冬<br>面料：羊毛、羊绒、灯芯绒<br>颜色：藏蓝、墨绿、驼色<br>示例：季节穿搭对比图（附温度区间）</div>
            <div class="keywords"><span class="keyword-tag">春夏</span><span class="keyword-tag">面料亚麻棉质</span><span class="keyword-tag">颜色浅蓝米白卡其</span><span class="keyword-tag">秋冬</span><span class="keyword-tag">面料羊毛羊绒灯芯绒</span></div>
        </div>

        <div class="slide content-slide">
            <h1>4. 体型适配技巧</h1>
            <div class="content">瘦高型：避免紧身，选择横条纹增加体积感<br>健壮型：V领拉伸颈部，避免宽肩设计<br>偏胖型：深色系+垂感面料，避免亮色<br>矮小型：高腰裤+短款外套，视觉拉长比例</div>
            <div class="keywords"><span class="keyword-tag">瘦高型避免紧身选择横条纹增加体积感</span><span class="keyword-tag">健壮型V领拉伸颈部避免宽肩设计</span><span class="keyword-tag">偏胖型深色系垂感面料避免亮色</span><span class="keyword-tag">矮小型高腰裤短款外套视觉拉长比例</span></div>
        </div>

        <div class="slide content-slide">
            <h1>5. 色彩搭配法则</h1>
            <div class="content">中性色基础：黑、白、灰、藏蓝的万能搭配<br>亮色点缀：领带/口袋巾/袜子提亮<br>禁忌：红配绿、荧光色大面积使用<br>工具：色彩轮盘示意图（PPT动画展示）</div>
            <div class="keywords"><span class="keyword-tag">中性色基础黑白灰藏蓝的万能搭配</span><span class="keyword-tag">亮色点缀领带口袋巾袜子提亮</span><span class="keyword-tag">禁忌红配绿荧光色大面积使用</span><span class="keyword-tag">工具色彩轮盘示意图PPT动画展示</span></div>
        </div>

        <div class="slide content-slide">
            <h1>6. 必备单品推荐</h1>
            <div class="content">10件基础单品清单<br>白衬衫<br>深色西装<br>修身牛仔裤<br>牛津鞋<br>纯色针织衫<br>...<br>搭配示例：单品组合案例（如“白衬衫+卡其裤+乐福鞋”）</div>
            <div class="keywords"><span class="keyword-tag">10件基础单品清单</span><span class="keyword-tag">白衬衫</span><span class="keyword-tag">深色西装</span><span class="keyword-tag">修身牛仔裤</span><span class="keyword-tag">牛津鞋</span></div>
        </div>

        <div class="slide content-slide">
            <h1>7. 品牌与预算选择</h1>
            <div class="content">高端：Armani、Hugo Boss（￥5000+）<br>中端：Zara、Massimo Dutti（￥500-2000）<br>平价：Uniqlo、H&M（￥100-500）<br>性价比推荐：单品价格与质量对比表</div>
            <div class="keywords"><span class="keyword-tag">高端ArmaniHugo</span><span class="keyword-tag">Boss5000</span><span class="keyword-tag">中端ZaraMassimo</span><span class="keyword-tag">Dutti5002000</span><span class="keyword-tag">平价UniqloHM100500</span></div>
        </div>

        <div class="slide content-slide">
            <h1>8. 常见错误与解决方案</h1>
            <div class="content">错误1：裤子过长堆积鞋面 → 修改裤脚或选择九分裤<br>错误2：衬衫肩线过宽 → 选择修身剪裁<br>错误3：全身超过3种颜色 → 回归中性色+1个亮色<br>对比图：错误案例 vs 修正后效果</div>
            <div class="keywords"><span class="keyword-tag">错误1裤子过长堆积鞋面</span><span class="keyword-tag">修改裤脚或选择九分裤</span><span class="keyword-tag">错误2衬衫肩线过宽</span><span class="keyword-tag">选择修身剪裁</span><span class="keyword-tag">错误3全身超过3种颜色</span></div>
        </div>

        <div class="slide summary-slide title-slide">
            <h1>总结</h1>
            <div class="content" style="text-align: center; margin: 40px 0;">基础穿衣原则 • 场合着装指南 • 季节与气候搭配 • 体型适配技巧 • 色彩搭配法则</div>
            <p style="margin-top: 60px; font-size: 2rem; font-weight: bold; color: #f0f8ff;">谢谢观看！</p>
        </div>

        <div class="controls">
            <button onclick="previousSlide()">◀ 上一张</button>
            <span class="slide-counter">
                <span id="current">1</span> / <span id="total">11</span>
            </span>
            <button onclick="nextSlide()">下一张 ▶</button>
            <button onclick="toggleFullscreen()">全屏</button>
            <button onclick="window.print()">打印</button>
        </div>
    </div>

    <script>
        let currentSlide = 0;
        const totalSlides = 11;

        function showSlide(index) {
            document.querySelectorAll('.slide').forEach((slide, i) => {
                slide.classList.toggle('active', i === index);
            });
            document.getElementById('current').textContent = index + 1;
        }

        function nextSlide() {
            if (currentSlide < totalSlides - 1) {
                currentSlide++;
                showSlide(currentSlide);
            }
        }

        function previousSlide() {
            if (currentSlide > 0) {
                currentSlide--;
                showSlide(currentSlide);
            }
        }

        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen();
            } else {
                document.exitFullscreen();
            }
        }

        // 键盘控制
        document.addEventListener('keydown', (e) => {
            switch(e.key) {
                case 'ArrowRight':
                case ' ':
                    nextSlide();
                    break;
                case 'ArrowLeft':
                    previousSlide();
                    break;
                case 'Escape':
                    if (document.fullscreenElement) {
                        document.exitFullscreen();
                    }
                    break;
            }
        });

        // 初始化
        showSlide(0);

        console.log('PPT演示文稿已加载');
        console.log('使用方向键或按钮控制幻灯片');
        console.log('按空格键或右箭头前进，左箭头后退');
        console.log('按ESC退出全屏模式');
    </script>
</body>
</html>