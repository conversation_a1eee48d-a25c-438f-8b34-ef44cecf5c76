<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>编码修复工具 - AI PPT Generator</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', 'Sim<PERSON>un', Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .fix-section {
            margin: 25px 0;
            padding: 20px;
            border: 1px solid #e2e8f0;
            border-radius: 10px;
            background: #f8fafc;
        }
        .fix-section h2 {
            color: #667eea;
            margin-bottom: 15px;
        }
        .input-group {
            margin: 15px 0;
        }
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #4a5568;
        }
        textarea {
            width: 100%;
            min-height: 120px;
            padding: 12px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-family: 'Microsoft YaHei', monospace;
            font-size: 14px;
            line-height: 1.5;
        }
        textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        button {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 8px 5px;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        button:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            white-space: pre-wrap;
            font-family: 'Microsoft YaHei', monospace;
            font-size: 13px;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            border-left: 4px solid #28a745;
            background: #d4edda;
        }
        .error {
            border-left: 4px solid #dc3545;
            background: #f8d7da;
        }
        .warning {
            border-left: 4px solid #ffc107;
            background: #fff3cd;
        }
        .encoding-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        .info-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            text-align: center;
        }
        .info-value {
            font-size: 1.2rem;
            font-weight: 600;
            color: #667eea;
        }
        .info-label {
            font-size: 0.9rem;
            color: #718096;
            margin-top: 5px;
        }
        .sample-text {
            background: #eef2ff;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            border-left: 4px solid #667eea;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 中文编码修复工具</h1>
        
        <div class="fix-section">
            <h2>📊 当前编码状态</h2>
            <div class="encoding-info" id="encodingInfo">
                <!-- 编码信息将在这里显示 -->
            </div>
        </div>

        <div class="fix-section">
            <h2>📝 文本编码测试</h2>
            <div class="input-group">
                <label for="testInput">输入测试文本（支持中文）：</label>
                <textarea id="testInput" placeholder="请输入包含中文的测试文本...

示例：
AI在日常生活中的应用
第1章：智能家居领域
1.1 语音助手设备控制
1.2 智能家电联动管理
1.3 家庭安防监控系统

第2章：医疗健康服务
2.1 疾病诊断辅助系统
2.2 个性化治疗方案生成
2.3 健康监测穿戴设备"></textarea>
            </div>
            
            <button onclick="testTextEncoding()">🔍 测试文本编码</button>
            <button onclick="fixTextEncoding()">🔧 修复编码问题</button>
            <button onclick="testWithAPI()">🚀 测试API处理</button>
            
            <div id="textResult" class="result"></div>
        </div>

        <div class="fix-section">
            <h2>🎯 常见问题修复</h2>
            <div class="sample-text">
                <strong>常见乱码问题：</strong><br>
                • 问题：A���ճ������е�Ӧ��<br>
                • 原因：UTF-8编码被错误解析为其他编码<br>
                • 解决：确保整个链路使用UTF-8编码
            </div>
            
            <button onclick="showCommonFixes()">📋 显示常见修复方案</button>
            <button onclick="testBrowserSupport()">🌐 测试浏览器支持</button>
            <button onclick="resetAllSettings()">🔄 重置所有设置</button>
            
            <div id="fixResult" class="result"></div>
        </div>

        <div class="fix-section">
            <h2>🔗 快速操作</h2>
            <button onclick="openMainApp()">🏠 返回主应用</button>
            <button onclick="openEncodingTest()">🧪 编码测试页面</button>
            <button onclick="downloadFixedFiles()">💾 下载修复文件</button>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3000/api';

        // 页面加载时显示编码信息
        window.onload = function() {
            showEncodingInfo();
        };

        function showEncodingInfo() {
            const infoDiv = document.getElementById('encodingInfo');
            
            infoDiv.innerHTML = `
                <div class="info-card">
                    <div class="info-value">${document.characterSet}</div>
                    <div class="info-label">文档字符集</div>
                </div>
                <div class="info-card">
                    <div class="info-value">${document.inputEncoding || 'UTF-8'}</div>
                    <div class="info-label">输入编码</div>
                </div>
                <div class="info-card">
                    <div class="info-value">${navigator.language}</div>
                    <div class="info-label">浏览器语言</div>
                </div>
                <div class="info-card">
                    <div class="info-value">${new Date().toLocaleString('zh-CN')}</div>
                    <div class="info-label">当前时间</div>
                </div>
            `;
        }

        function testTextEncoding() {
            const input = document.getElementById('testInput').value;
            const resultDiv = document.getElementById('textResult');
            
            if (!input.trim()) {
                resultDiv.className = 'result warning';
                resultDiv.textContent = '⚠️ 请输入测试文本';
                return;
            }

            try {
                // 测试文本编码
                const encoder = new TextEncoder();
                const decoder = new TextDecoder();
                const encoded = encoder.encode(input);
                const decoded = decoder.decode(encoded);
                
                const isValid = input === decoded;
                
                resultDiv.className = isValid ? 'result success' : 'result error';
                resultDiv.textContent = `${isValid ? '✅' : '❌'} 编码测试结果
                
原始文本长度: ${input.length}
编码后字节数: ${encoded.length}
解码后文本: ${decoded}
编码是否正确: ${isValid ? '是' : '否'}

字符详情:
${Array.from(input).slice(0, 20).map((char, i) => 
    `${i + 1}. '${char}' (Unicode: U+${char.charCodeAt(0).toString(16).toUpperCase().padStart(4, '0')})`
).join('\n')}${input.length > 20 ? '\n...' : ''}`;

            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 编码测试失败: ${error.message}`;
            }
        }

        function fixTextEncoding() {
            const input = document.getElementById('testInput').value;
            const resultDiv = document.getElementById('textResult');
            
            if (!input.trim()) {
                resultDiv.className = 'result warning';
                resultDiv.textContent = '⚠️ 请输入需要修复的文本';
                return;
            }

            try {
                // 尝试修复常见的编码问题
                let fixed = input;
                
                // 修复常见的UTF-8乱码
                const fixes = [
                    { pattern: /Ã¢â‚¬â„¢/g, replacement: ''' },
                    { pattern: /Ã¢â‚¬Å"/g, replacement: '"' },
                    { pattern: /Ã¢â‚¬Â/g, replacement: '"' },
                    { pattern: /â€™/g, replacement: ''' },
                    { pattern: /â€œ/g, replacement: '"' },
                    { pattern: /â€/g, replacement: '"' },
                ];
                
                fixes.forEach(fix => {
                    fixed = fixed.replace(fix.pattern, fix.replacement);
                });
                
                // 更新输入框
                document.getElementById('testInput').value = fixed;
                
                resultDiv.className = 'result success';
                resultDiv.textContent = `✅ 编码修复完成
                
原始文本: ${input.substring(0, 100)}${input.length > 100 ? '...' : ''}
修复后文本: ${fixed.substring(0, 100)}${fixed.length > 100 ? '...' : ''}
修复了 ${input !== fixed ? '一些' : '0个'} 编码问题`;

            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 编码修复失败: ${error.message}`;
            }
        }

        async function testWithAPI() {
            const input = document.getElementById('testInput').value;
            const resultDiv = document.getElementById('textResult');
            
            if (!input.trim()) {
                resultDiv.className = 'result warning';
                resultDiv.textContent = '⚠️ 请输入测试文本';
                return;
            }

            resultDiv.textContent = '正在测试API编码处理...';

            try {
                const response = await fetch(`${API_BASE}/generate-ppt`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json; charset=utf-8'
                    },
                    body: JSON.stringify({
                        text: input,
                        templateId: 1,
                        options: { autoLayout: true }
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ API编码处理正常
                    
生成幻灯片: ${data.slides.length} 张
模板: ${data.metadata.template.name}
AI分析:
- 关键词: ${data.metadata.aiAnalysis.keywordCount} 个
- 段落: ${data.metadata.aiAnalysis.sectionCount} 个
- 情感: ${data.metadata.aiAnalysis.sentiment}

幻灯片标题:
${data.slides.map((slide, i) => `${i + 1}. ${slide.title}`).join('\n')}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ API处理失败: ${data.error}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ API测试失败: ${error.message}`;
            }
        }

        function showCommonFixes() {
            const resultDiv = document.getElementById('fixResult');
            
            resultDiv.className = 'result success';
            resultDiv.textContent = `📋 常见编码问题修复方案

1. 浏览器设置
   - 确保浏览器编码设置为 UTF-8
   - 检查开发者工具中的字符编码

2. 文件编码
   - 所有 HTML/CSS/JS 文件保存为 UTF-8 编码
   - 确保没有 BOM (Byte Order Mark)

3. 服务器配置
   - Express 设置正确的 Content-Type 头
   - 确保数据库连接使用 UTF-8

4. 前端处理
   - 使用 TextEncoder/TextDecoder API
   - 正确处理 JSON 数据编码

5. Python 脚本
   - 确保 Python 文件使用 UTF-8 编码
   - 正确处理命令行参数编码

修复步骤：
1. 检查所有文件编码
2. 更新服务器配置
3. 测试 API 响应
4. 验证前端显示`;
        }

        function testBrowserSupport() {
            const resultDiv = document.getElementById('fixResult');
            
            const tests = [
                { name: 'TextEncoder 支持', test: () => typeof TextEncoder !== 'undefined' },
                { name: 'TextDecoder 支持', test: () => typeof TextDecoder !== 'undefined' },
                { name: 'Fetch API 支持', test: () => typeof fetch !== 'undefined' },
                { name: 'UTF-8 字符显示', test: () => '中文测试' === '中文测试' },
                { name: 'JSON UTF-8 支持', test: () => JSON.stringify({test: '中文'}).includes('中文') }
            ];
            
            const results = tests.map(test => ({
                name: test.name,
                passed: test.test(),
                status: test.test() ? '✅' : '❌'
            }));
            
            resultDiv.className = 'result success';
            resultDiv.textContent = `🌐 浏览器编码支持测试

${results.map(r => `${r.status} ${r.name}: ${r.passed ? '支持' : '不支持'}`).join('\n')}

浏览器信息:
- 用户代理: ${navigator.userAgent.substring(0, 80)}...
- 语言: ${navigator.language}
- 平台: ${navigator.platform}
- 字符集: ${document.characterSet}`;
        }

        function resetAllSettings() {
            document.getElementById('testInput').value = '';
            document.getElementById('textResult').textContent = '';
            document.getElementById('fixResult').textContent = '';
            
            const resultDiv = document.getElementById('fixResult');
            resultDiv.className = 'result success';
            resultDiv.textContent = '🔄 所有设置已重置';
        }

        function openMainApp() {
            window.open('http://localhost:3000', '_blank');
        }

        function openEncodingTest() {
            window.open('encoding-test.html', '_blank');
        }

        function downloadFixedFiles() {
            alert('💾 文件下载功能开发中...\n\n当前可以通过以下方式获取修复后的文件：\n1. 复制修复后的文本\n2. 手动保存为 UTF-8 编码文件\n3. 使用开发者工具导出');
        }
    </script>
</body>
</html>
