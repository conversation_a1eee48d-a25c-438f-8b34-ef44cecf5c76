// PPT导出器 - 将生成的幻灯片导出为HTML演示文稿
const fs = require('fs');
const path = require('path');

class PPTExporter {
    constructor() {
        this.outputDir = path.join(__dirname, '../exports');
        this.ensureOutputDir();
    }

    // 确保输出目录存在
    ensureOutputDir() {
        if (!fs.existsSync(this.outputDir)) {
            fs.mkdirSync(this.outputDir, { recursive: true });
            console.log('📁 创建导出目录:', this.outputDir);
        }
    }

    // 导出PPT文件（HTML格式）
    async exportToPPTX(slides, metadata = {}) {
        try {
            console.log('🚀 开始生成PPT文件...');

            // 生成HTML格式的PPT
            const htmlContent = this.generateHTMLPresentation(slides, metadata);

            // 生成文件名
            const fileName = this.generateFileName(metadata.title, 'html');
            const filePath = path.join(this.outputDir, fileName);

            // 写入文件
            fs.writeFileSync(filePath, htmlContent, 'utf8');

            console.log('✅ PPT文件生成成功:', fileName);

            return {
                success: true,
                fileName: fileName,
                filePath: filePath,
                fileSize: fs.statSync(filePath).size,
                slideCount: slides.length,
                format: 'html'
            };

        } catch (error) {
            console.error('❌ PPT导出错误:', error);
            throw error;
        }
    }

    // 生成HTML演示文稿
    generateHTMLPresentation(slides, metadata) {
        const title = metadata.title || 'AI生成的PPT';
        const author = metadata.author || 'AI PPT Generator';

        const slidesHTML = slides.map((slide, index) =>
            this.generateSlideHTML(slide, index)
        ).join('\n');

        return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${title}</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #000;
            color: #fff;
            overflow: hidden;
        }
        .presentation {
            width: 100vw;
            height: 100vh;
            position: relative;
        }
        .slide {
            width: 100%;
            height: 100%;
            display: none;
            padding: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            position: absolute;
            top: 0;
            left: 0;
        }
        .slide.active { display: flex; flex-direction: column; justify-content: center; }
        .slide.title-slide { text-align: center; }
        .slide h1 {
            font-size: 3rem;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .slide h2 {
            font-size: 2rem;
            margin-bottom: 20px;
            color: #f0f8ff;
        }
        .slide .content {
            font-size: 1.5rem;
            line-height: 1.8;
            margin: 20px 0;
        }
        .slide .keywords {
            margin-top: 30px;
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            justify-content: center;
        }
        .keyword-tag {
            background: rgba(255,255,255,0.2);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 1rem;
        }
        .controls {
            position: fixed;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1000;
        }
        .controls button {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            padding: 10px 20px;
            margin: 0 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1rem;
        }
        .controls button:hover { background: rgba(255,255,255,0.3); }
        .slide-counter {
            background: rgba(0,0,0,0.5);
            padding: 5px 15px;
            border-radius: 15px;
            margin: 0 10px;
        }
        .print-mode .slide {
            display: block !important;
            page-break-after: always;
            position: static;
            height: auto;
            min-height: 100vh;
        }
        @media print {
            .controls { display: none; }
            .slide {
                display: block !important;
                page-break-after: always;
                position: static;
                height: auto;
                min-height: 100vh;
            }
        }
    </style>
</head>
<body>
    <div class="presentation">
        ${slidesHTML}

        <div class="controls">
            <button onclick="previousSlide()">◀ 上一张</button>
            <span class="slide-counter">
                <span id="current">1</span> / <span id="total">${slides.length}</span>
            </span>
            <button onclick="nextSlide()">下一张 ▶</button>
            <button onclick="toggleFullscreen()">全屏</button>
            <button onclick="window.print()">打印</button>
        </div>
    </div>

    <script>
        let currentSlide = 0;
        const totalSlides = ${slides.length};

        function showSlide(index) {
            document.querySelectorAll('.slide').forEach((slide, i) => {
                slide.classList.toggle('active', i === index);
            });
            document.getElementById('current').textContent = index + 1;
        }

        function nextSlide() {
            if (currentSlide < totalSlides - 1) {
                currentSlide++;
                showSlide(currentSlide);
            }
        }

        function previousSlide() {
            if (currentSlide > 0) {
                currentSlide--;
                showSlide(currentSlide);
            }
        }

        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen();
            } else {
                document.exitFullscreen();
            }
        }

        // 键盘控制
        document.addEventListener('keydown', (e) => {
            switch(e.key) {
                case 'ArrowRight':
                case ' ':
                    nextSlide();
                    break;
                case 'ArrowLeft':
                    previousSlide();
                    break;
                case 'Escape':
                    if (document.fullscreenElement) {
                        document.exitFullscreen();
                    }
                    break;
            }
        });

        // 初始化
        showSlide(0);

        console.log('PPT演示文稿已加载');
        console.log('使用方向键或按钮控制幻灯片');
        console.log('按空格键或右箭头前进，左箭头后退');
        console.log('按ESC退出全屏模式');
    </script>
</body>
</html>`;
    }

    // 生成单个幻灯片的HTML
    generateSlideHTML(slide, index) {
        console.log(`📄 生成幻灯片 ${index + 1}: ${slide.title}`);

        const slideClass = `slide ${slide.type}-slide`;

        switch (slide.type) {
            case 'title':
                return this.createTitleSlideHTML(slide, slideClass);
            case 'content':
                return this.createContentSlideHTML(slide, slideClass);
            case 'summary':
                return this.createSummarySlideHTML(slide, slideClass);
            case 'image':
                return this.createImageSlideHTML(slide, slideClass);
            default:
                return this.createContentSlideHTML(slide, slideClass);
        }
    }

    // 创建标题幻灯片HTML
    createTitleSlideHTML(slide, slideClass) {
        const title = this.escapeHtml(slide.title || '标题');
        const subtitle = slide.subtitle ? this.escapeHtml(slide.subtitle) : '';
        const author = slide.author ? this.escapeHtml(slide.author) : '';

        return `
        <div class="${slideClass} title-slide">
            <h1>${title}</h1>
            ${subtitle ? `<h2>${subtitle}</h2>` : ''}
            ${author ? `<p style="margin-top: 40px; font-size: 1.2rem; opacity: 0.8;">${author}</p>` : ''}
        </div>`;
    }

    // 创建内容幻灯片HTML
    createContentSlideHTML(slide, slideClass) {
        const title = this.escapeHtml(slide.title || '内容');
        const content = slide.content ? this.formatContentForHTML(slide.content) : '';
        const keywords = slide.keywords && slide.keywords.length > 0
            ? slide.keywords.map(k => `<span class="keyword-tag">${this.escapeHtml(k)}</span>`).join('')
            : '';

        return `
        <div class="${slideClass}">
            <h1>${title}</h1>
            ${content ? `<div class="content">${content}</div>` : ''}
            ${keywords ? `<div class="keywords">${keywords}</div>` : ''}
        </div>`;
    }

    // 创建总结幻灯片HTML
    createSummarySlideHTML(slide, slideClass) {
        const title = this.escapeHtml(slide.title || '总结');
        const content = slide.content ? this.formatContentForHTML(slide.content) : '';

        return `
        <div class="${slideClass} title-slide">
            <h1>${title}</h1>
            ${content ? `<div class="content" style="text-align: center; margin: 40px 0;">${content}</div>` : ''}
            <p style="margin-top: 60px; font-size: 2rem; font-weight: bold; color: #f0f8ff;">谢谢观看！</p>
        </div>`;
    }

    // 创建图片幻灯片HTML
    createImageSlideHTML(slide, slideClass) {
        const title = slide.title ? this.escapeHtml(slide.title) : '';
        const caption = slide.caption ? this.escapeHtml(slide.caption) : '';

        return `
        <div class="${slideClass}">
            ${title ? `<h1 style="text-align: center;">${title}</h1>` : ''}
            <div style="display: flex; align-items: center; justify-content: center; flex: 1; margin: 40px 0;">
                <div style="background: rgba(255,255,255,0.1); border: 2px dashed rgba(255,255,255,0.3); border-radius: 10px; padding: 60px; text-align: center; font-size: 1.5rem;">
                    📷 图片位置
                </div>
            </div>
            ${caption ? `<p style="text-align: center; font-style: italic; opacity: 0.8;">${caption}</p>` : ''}
        </div>`;
    }

    // 格式化HTML内容
    formatContentForHTML(content) {
        if (!content) return '';

        // 处理换行和列表
        let formatted = content
            .replace(/\n\n/g, '<br><br>')  // 段落换行
            .replace(/\n/g, '<br>')        // 普通换行
            .replace(/^[\s]*[-•]\s*/gm, '• ')  // 统一列表符号
            .replace(/^[\s]*\d+\.\s*/gm, (match) => {
                const num = match.match(/\d+/)[0];
                return `${num}. `;
            });

        return formatted;
    }

    // HTML转义
    escapeHtml(text) {
        if (!text) return '';
        return text
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#39;');
    }

    // 生成文件名
    generateFileName(title, extension = 'html') {
        const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
        const safeName = (title || 'AI生成的PPT')
            .replace(/[^\w\s-]/g, '')  // 移除特殊字符
            .replace(/\s+/g, '_')      // 空格替换为下划线
            .slice(0, 50);             // 限制长度

        return `${safeName}_${timestamp}.${extension}`;
    }

    // 获取导出文件列表
    getExportedFiles() {
        try {
            const files = fs.readdirSync(this.outputDir)
                .filter(file => file.endsWith('.html') || file.endsWith('.pptx'))
                .map(file => {
                    const filePath = path.join(this.outputDir, file);
                    const stats = fs.statSync(filePath);
                    return {
                        name: file,
                        path: filePath,
                        size: stats.size,
                        created: stats.birthtime,
                        modified: stats.mtime,
                        type: file.endsWith('.html') ? 'html' : 'pptx'
                    };
                })
                .sort((a, b) => b.created - a.created);  // 按创建时间倒序

            return files;
        } catch (error) {
            console.error('获取导出文件列表失败:', error);
            return [];
        }
    }

    // 清理旧文件
    cleanupOldFiles(maxFiles = 10) {
        try {
            const files = this.getExportedFiles();

            if (files.length > maxFiles) {
                const filesToDelete = files.slice(maxFiles);

                filesToDelete.forEach(file => {
                    fs.unlinkSync(file.path);
                    console.log('🗑️ 删除旧文件:', file.name);
                });

                console.log(`🧹 清理完成，删除了 ${filesToDelete.length} 个旧文件`);
            }
        } catch (error) {
            console.error('清理旧文件失败:', error);
        }
    }
}

module.exports = PPTExporter;
