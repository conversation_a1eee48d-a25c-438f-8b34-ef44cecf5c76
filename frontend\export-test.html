<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PPT导出功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 3px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            border-left: 4px solid #28a745;
        }
        .error {
            border-left: 4px solid #dc3545;
        }
        .file-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        .file-item {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
        }
        .file-item h4 {
            margin: 0 0 10px 0;
            color: #495057;
        }
        .file-info {
            font-size: 0.9rem;
            color: #6c757d;
            margin: 5px 0;
        }
        .download-btn {
            background: #28a745;
            font-size: 0.9rem;
            padding: 5px 10px;
        }
        .download-btn:hover {
            background: #218838;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 PPT导出功能测试</h1>
        
        <div class="test-section">
            <h2>1. 生成测试PPT内容</h2>
            <button onclick="generateTestSlides()">生成测试幻灯片</button>
            <div id="slidesResult" class="result"></div>
        </div>

        <div class="test-section">
            <h2>2. 导出PPT文件</h2>
            <button id="exportBtn" onclick="exportPPT()" disabled>导出为HTML演示文稿</button>
            <div id="exportResult" class="result"></div>
        </div>

        <div class="test-section">
            <h2>3. 已导出的文件</h2>
            <button onclick="loadExportedFiles()">刷新文件列表</button>
            <div id="filesList" class="file-list"></div>
        </div>

        <div class="test-section">
            <h2>4. 测试API</h2>
            <button onclick="testExportAPI()">测试导出API</button>
            <button onclick="testDownloadAPI()">测试下载API</button>
            <div id="apiResult" class="result"></div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/axios/1.6.0/axios.min.js"></script>
    <script>
        let testSlides = [];

        function generateTestSlides() {
            const resultDiv = document.getElementById('slidesResult');
            resultDiv.textContent = '正在生成测试幻灯片...';
            
            testSlides = [
                {
                    id: 1,
                    type: 'title',
                    title: 'AI PPT 导出测试',
                    subtitle: '测试HTML演示文稿导出功能',
                    author: 'AI PPT Generator'
                },
                {
                    id: 2,
                    type: 'content',
                    title: '功能特点',
                    content: '• 支持多种幻灯片类型\n• 专业的演示界面\n• 键盘快捷键控制\n• 全屏播放模式\n• 打印支持',
                    keywords: ['功能', '特点', '演示']
                },
                {
                    id: 3,
                    type: 'content',
                    title: '技术实现',
                    content: '1. 后端Node.js生成HTML\n2. 前端JavaScript控制\n3. CSS样式美化\n4. 响应式设计',
                    keywords: ['技术', '实现', 'HTML']
                },
                {
                    id: 4,
                    type: 'image',
                    title: '图片展示',
                    caption: '这里可以放置图片内容'
                },
                {
                    id: 5,
                    type: 'summary',
                    title: '总结',
                    content: '✅ 导出功能正常\n✅ 演示效果良好\n✅ 用户体验优秀'
                }
            ];
            
            resultDiv.className = 'result success';
            resultDiv.textContent = `✅ 成功生成 ${testSlides.length} 张测试幻灯片：\n\n${testSlides.map((slide, i) => `${i + 1}. ${slide.title} (${slide.type})`).join('\n')}`;
            
            document.getElementById('exportBtn').disabled = false;
        }

        async function exportPPT() {
            if (testSlides.length === 0) {
                alert('请先生成测试幻灯片');
                return;
            }

            const resultDiv = document.getElementById('exportResult');
            resultDiv.textContent = '正在导出PPT...';
            
            try {
                const exportData = {
                    slides: testSlides,
                    metadata: {
                        title: 'AI PPT 导出测试',
                        author: 'AI PPT Generator',
                        subject: '测试演示文稿',
                        createdAt: new Date().toISOString()
                    },
                    format: 'pptx'
                };

                const response = await axios.post('/api/export-ppt', exportData, {
                    timeout: 30000
                });

                if (response.data.success) {
                    const file = response.data.file;
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 导出成功！\n\n文件名: ${file.name}\n文件大小: ${formatFileSize(file.size)}\n幻灯片数量: ${file.slideCount}\n下载链接: ${file.downloadUrl}`;
                    
                    // 自动下载
                    downloadFile(file.downloadUrl, file.name);
                    
                    // 刷新文件列表
                    setTimeout(loadExportedFiles, 1000);
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 导出失败: ${response.data.error}`;
                }

            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 导出错误: ${error.message}`;
            }
        }

        async function loadExportedFiles() {
            const filesList = document.getElementById('filesList');
            filesList.innerHTML = '<p>正在加载文件列表...</p>';
            
            try {
                const response = await axios.get('/api/exports');
                
                if (response.data.success && response.data.files.length > 0) {
                    filesList.innerHTML = response.data.files.map(file => `
                        <div class="file-item">
                            <h4>📄 ${file.name}</h4>
                            <div class="file-info">大小: ${formatFileSize(file.size)}</div>
                            <div class="file-info">创建时间: ${new Date(file.created).toLocaleString()}</div>
                            <div class="file-info">类型: ${file.type || 'unknown'}</div>
                            <button class="download-btn" onclick="downloadFile('${file.downloadUrl}', '${file.name}')">
                                📥 下载文件
                            </button>
                            <button class="download-btn" onclick="openFile('${file.downloadUrl}')" style="background: #17a2b8;">
                                👁️ 在线查看
                            </button>
                        </div>
                    `).join('');
                } else {
                    filesList.innerHTML = '<p>暂无导出文件</p>';
                }
                
            } catch (error) {
                filesList.innerHTML = `<p>加载失败: ${error.message}</p>`;
            }
        }

        async function testExportAPI() {
            const resultDiv = document.getElementById('apiResult');
            resultDiv.textContent = '测试导出API...';
            
            const testData = {
                slides: [
                    {
                        id: 1,
                        type: 'title',
                        title: 'API测试',
                        subtitle: '测试导出API功能'
                    }
                ],
                metadata: {
                    title: 'API测试',
                    author: 'Test'
                },
                format: 'pptx'
            };
            
            try {
                const response = await axios.post('/api/export-ppt', testData);
                
                resultDiv.className = 'result success';
                resultDiv.textContent = `✅ API测试成功：\n\n${JSON.stringify(response.data, null, 2)}`;
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ API测试失败: ${error.message}`;
            }
        }

        async function testDownloadAPI() {
            const resultDiv = document.getElementById('apiResult');
            resultDiv.textContent = '测试下载API...';
            
            try {
                const response = await axios.get('/api/exports');
                
                if (response.data.success && response.data.files.length > 0) {
                    const firstFile = response.data.files[0];
                    
                    // 测试HEAD请求
                    const headResponse = await axios.head(firstFile.downloadUrl);
                    
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 下载API测试成功：\n\n文件: ${firstFile.name}\n状态: ${headResponse.status}\n内容类型: ${headResponse.headers['content-type'] || '未知'}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = '❌ 没有可测试的文件';
                }
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 下载API测试失败: ${error.message}`;
            }
        }

        function downloadFile(url, filename) {
            const link = document.createElement('a');
            link.href = url;
            link.download = filename;
            link.style.display = 'none';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            console.log('下载已触发:', filename);
        }

        function openFile(url) {
            window.open(url, '_blank');
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 页面加载时自动加载文件列表
        window.addEventListener('load', () => {
            loadExportedFiles();
        });
    </script>
</body>
</html>
