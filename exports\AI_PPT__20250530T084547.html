<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI PPT 导出测试</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #000;
            color: #fff;
            overflow: hidden;
        }
        .presentation {
            width: 100vw;
            height: 100vh;
            position: relative;
        }
        .slide {
            width: 100%;
            height: 100%;
            display: none;
            padding: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            position: absolute;
            top: 0;
            left: 0;
        }
        .slide.active { display: flex; flex-direction: column; justify-content: center; }
        .slide.title-slide { text-align: center; }
        .slide h1 {
            font-size: 3rem;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .slide h2 {
            font-size: 2rem;
            margin-bottom: 20px;
            color: #f0f8ff;
        }
        .slide .content {
            font-size: 1.5rem;
            line-height: 1.8;
            margin: 20px 0;
        }
        .slide .keywords {
            margin-top: 30px;
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            justify-content: center;
        }
        .keyword-tag {
            background: rgba(255,255,255,0.2);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 1rem;
        }
        .controls {
            position: fixed;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1000;
        }
        .controls button {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            padding: 10px 20px;
            margin: 0 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1rem;
        }
        .controls button:hover { background: rgba(255,255,255,0.3); }
        .slide-counter {
            background: rgba(0,0,0,0.5);
            padding: 5px 15px;
            border-radius: 15px;
            margin: 0 10px;
        }
        .print-mode .slide {
            display: block !important;
            page-break-after: always;
            position: static;
            height: auto;
            min-height: 100vh;
        }
        @media print {
            .controls { display: none; }
            .slide {
                display: block !important;
                page-break-after: always;
                position: static;
                height: auto;
                min-height: 100vh;
            }
        }
    </style>
</head>
<body>
    <div class="presentation">
        
        <div class="slide title-slide title-slide">
            <h1>AI PPT 导出测试</h1>
            <h2>测试HTML演示文稿导出功能</h2>
            <p style="margin-top: 40px; font-size: 1.2rem; opacity: 0.8;">AI PPT Generator</p>
        </div>

        <div class="slide content-slide">
            <h1>功能特点</h1>
            <div class="content">• 支持多种幻灯片类型<br>• 专业的演示界面<br>• 键盘快捷键控制<br>• 全屏播放模式<br>• 打印支持</div>
            <div class="keywords"><span class="keyword-tag">功能</span><span class="keyword-tag">特点</span><span class="keyword-tag">演示</span></div>
        </div>

        <div class="slide content-slide">
            <h1>技术实现</h1>
            <div class="content">1. 后端Node.js生成HTML<br>2. 前端JavaScript控制<br>3. CSS样式美化<br>4. 响应式设计</div>
            <div class="keywords"><span class="keyword-tag">技术</span><span class="keyword-tag">实现</span><span class="keyword-tag">HTML</span></div>
        </div>

        <div class="slide image-slide">
            <h1 style="text-align: center;">图片展示</h1>
            <div style="display: flex; align-items: center; justify-content: center; flex: 1; margin: 40px 0;">
                <div style="background: rgba(255,255,255,0.1); border: 2px dashed rgba(255,255,255,0.3); border-radius: 10px; padding: 60px; text-align: center; font-size: 1.5rem;">
                    📷 图片位置
                </div>
            </div>
            <p style="text-align: center; font-style: italic; opacity: 0.8;">这里可以放置图片内容</p>
        </div>

        <div class="slide summary-slide title-slide">
            <h1>总结</h1>
            <div class="content" style="text-align: center; margin: 40px 0;">✅ 导出功能正常<br>✅ 演示效果良好<br>✅ 用户体验优秀</div>
            <p style="margin-top: 60px; font-size: 2rem; font-weight: bold; color: #f0f8ff;">谢谢观看！</p>
        </div>

        <div class="controls">
            <button onclick="previousSlide()">◀ 上一张</button>
            <span class="slide-counter">
                <span id="current">1</span> / <span id="total">5</span>
            </span>
            <button onclick="nextSlide()">下一张 ▶</button>
            <button onclick="toggleFullscreen()">全屏</button>
            <button onclick="window.print()">打印</button>
        </div>
    </div>

    <script>
        let currentSlide = 0;
        const totalSlides = 5;

        function showSlide(index) {
            document.querySelectorAll('.slide').forEach((slide, i) => {
                slide.classList.toggle('active', i === index);
            });
            document.getElementById('current').textContent = index + 1;
        }

        function nextSlide() {
            if (currentSlide < totalSlides - 1) {
                currentSlide++;
                showSlide(currentSlide);
            }
        }

        function previousSlide() {
            if (currentSlide > 0) {
                currentSlide--;
                showSlide(currentSlide);
            }
        }

        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen();
            } else {
                document.exitFullscreen();
            }
        }

        // 键盘控制
        document.addEventListener('keydown', (e) => {
            switch(e.key) {
                case 'ArrowRight':
                case ' ':
                    nextSlide();
                    break;
                case 'ArrowLeft':
                    previousSlide();
                    break;
                case 'Escape':
                    if (document.fullscreenElement) {
                        document.exitFullscreen();
                    }
                    break;
            }
        });

        // 初始化
        showSlide(0);

        console.log('PPT演示文稿已加载');
        console.log('使用方向键或按钮控制幻灯片');
        console.log('按空格键或右箭头前进，左箭头后退');
        console.log('按ESC退出全屏模式');
    </script>
</body>
</html>