<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI PPT 生成器 - 简化版</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            color: white;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 20px;
        }

        .section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .section h2 {
            color: #4a5568;
            margin-bottom: 20px;
            font-size: 1.3rem;
        }

        textarea {
            width: 100%;
            min-height: 200px;
            padding: 15px;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            font-size: 14px;
            line-height: 1.6;
            resize: vertical;
            font-family: inherit;
        }

        textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .generate-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 20px;
        }

        .generate-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }

        .preview-area {
            min-height: 400px;
            border: 2px dashed #e2e8f0;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8fafc;
            position: relative;
        }

        .placeholder {
            text-align: center;
            color: #a0aec0;
        }

        .placeholder i {
            font-size: 3rem;
            margin-bottom: 15px;
        }

        .slide {
            width: 100%;
            max-width: 600px;
            aspect-ratio: 16/9;
            background: white;
            border-radius: 8px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            display: none;
            padding: 30px;
            overflow: hidden;
        }

        .slide.active {
            display: block;
        }

        .slide-title {
            font-size: 1.8rem;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 20px;
            text-align: center;
        }

        .slide-content {
            font-size: 1rem;
            line-height: 1.6;
            color: #4a5568;
        }

        .navigation {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 20px;
        }

        .nav-btn {
            background: #667eea;
            color: white;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .nav-btn:hover {
            background: #5a67d8;
            transform: scale(1.1);
        }

        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .status-bar {
            background: rgba(255,255,255,0.9);
            padding: 10px 20px;
            border-radius: 10px;
            text-align: center;
            font-size: 0.9rem;
            color: #4a5568;
        }

        .hidden {
            display: none !important;
        }

        .loading {
            text-align: center;
            color: #667eea;
        }

        .loading i {
            font-size: 2rem;
            margin-bottom: 15px;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1><i class="fas fa-magic"></i> AI PPT 生成器</h1>
            <p>智能分析您的内容，自动生成专业演示文稿</p>
        </header>

        <main class="main-content">
            <section class="section">
                <h2><i class="fas fa-edit"></i> 输入内容</h2>
                <textarea id="textInput" placeholder="请输入您要制作PPT的内容...

例如：
公司年度总结报告
2023年业绩回顾

一、销售业绩
今年销售额达到1000万元，同比增长20%

二、市场拓展
新开拓了5个城市市场，客户满意度达到95%

三、未来规划
计划明年继续扩大市场份额，目标增长30%"></textarea>
                
                <button id="generateBtn" class="generate-btn">
                    <i class="fas fa-wand-magic-sparkles"></i>
                    生成 PPT
                </button>
            </section>

            <section class="section">
                <h2><i class="fas fa-eye"></i> 预览</h2>
                <div id="loadingIndicator" class="loading hidden">
                    <i class="fas fa-spinner fa-spin"></i>
                    <p>AI正在分析您的内容并生成PPT...</p>
                </div>
                
                <div id="previewArea" class="preview-area">
                    <div class="placeholder">
                        <i class="fas fa-presentation-screen"></i>
                        <p>生成的PPT预览将在这里显示</p>
                    </div>
                </div>

                <div id="slideNavigation" class="navigation hidden">
                    <button id="prevSlide" class="nav-btn">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <span id="slideCounter">1 / 1</span>
                    <button id="nextSlide" class="nav-btn">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </section>
        </main>

        <div id="statusBar" class="status-bar">
            <span id="statusText">就绪</span>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/axios/1.6.0/axios.min.js"></script>
    <script>
        class SimplePPTGenerator {
            constructor() {
                this.currentSlide = 0;
                this.slides = [];
                this.init();
            }

            init() {
                this.bindEvents();
                this.updateStatus('就绪 - 简化版界面');
            }

            bindEvents() {
                document.getElementById('generateBtn').addEventListener('click', () => {
                    this.generatePPT();
                });

                document.getElementById('prevSlide').addEventListener('click', () => {
                    this.previousSlide();
                });

                document.getElementById('nextSlide').addEventListener('click', () => {
                    this.nextSlide();
                });
            }

            async generatePPT() {
                const text = document.getElementById('textInput').value.trim();
                
                if (!text) {
                    alert('请输入要生成PPT的内容');
                    return;
                }

                try {
                    this.showLoading(true);
                    this.updateStatus('正在生成PPT...');

                    const response = await axios.post('/api/generate-ppt', {
                        text: text,
                        templateId: 1,
                        options: {
                            autoLayout: true,
                            includeImages: true,
                            addTransitions: false
                        }
                    });

                    if (response.data.success) {
                        this.slides = response.data.slides;
                        this.renderSlides();
                        this.updateStatus(`PPT生成成功！共 ${this.slides.length} 张幻灯片`);
                    } else {
                        this.updateStatus(`生成失败: ${response.data.error}`);
                    }

                } catch (error) {
                    console.error('生成PPT错误:', error);
                    this.updateStatus('生成PPT时发生错误，请检查网络连接');
                } finally {
                    this.showLoading(false);
                }
            }

            renderSlides() {
                const previewArea = document.getElementById('previewArea');
                previewArea.innerHTML = '';

                this.slides.forEach((slide, index) => {
                    const slideElement = document.createElement('div');
                    slideElement.className = `slide ${index === 0 ? 'active' : ''}`;
                    slideElement.dataset.slideIndex = index;

                    slideElement.innerHTML = `
                        <h1 class="slide-title">${slide.title || '幻灯片'}</h1>
                        <div class="slide-content">${this.formatContent(slide.content || '')}</div>
                    `;

                    previewArea.appendChild(slideElement);
                });

                this.currentSlide = 0;
                this.updateNavigation();
                document.getElementById('slideNavigation').classList.remove('hidden');
            }

            formatContent(content) {
                if (!content) return '';
                return content.replace(/\n/g, '<br>');
            }

            previousSlide() {
                if (this.currentSlide > 0) {
                    this.goToSlide(this.currentSlide - 1);
                }
            }

            nextSlide() {
                if (this.currentSlide < this.slides.length - 1) {
                    this.goToSlide(this.currentSlide + 1);
                }
            }

            goToSlide(index) {
                this.currentSlide = index;
                
                document.querySelectorAll('.slide').forEach((slide, i) => {
                    slide.classList.toggle('active', i === index);
                });
                
                this.updateNavigation();
            }

            updateNavigation() {
                const counter = document.getElementById('slideCounter');
                const prevBtn = document.getElementById('prevSlide');
                const nextBtn = document.getElementById('nextSlide');

                counter.textContent = `${this.currentSlide + 1} / ${this.slides.length}`;
                prevBtn.disabled = this.currentSlide === 0;
                nextBtn.disabled = this.currentSlide === this.slides.length - 1;
            }

            showLoading(show) {
                const loading = document.getElementById('loadingIndicator');
                const preview = document.getElementById('previewArea');
                
                if (show) {
                    loading.classList.remove('hidden');
                    preview.style.display = 'none';
                } else {
                    loading.classList.add('hidden');
                    preview.style.display = 'flex';
                }
            }

            updateStatus(message) {
                document.getElementById('statusText').textContent = message;
            }
        }

        // 初始化应用
        document.addEventListener('DOMContentLoaded', () => {
            new SimplePPTGenerator();
        });
    </script>
</body>
</html>
