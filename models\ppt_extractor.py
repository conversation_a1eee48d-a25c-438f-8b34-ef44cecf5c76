#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PPT文件内容提取器
从PPT文件中提取文本、图片和结构信息
"""

import sys
import json
import os
from pathlib import Path

# 尝试导入python-pptx库
try:
    from pptx import Presentation
    from pptx.enum.shapes import MSO_SHAPE_TYPE
    HAS_PPTX = True
except ImportError:
    HAS_PPTX = False
    print("警告: python-pptx未安装，使用基础解析", file=sys.stderr)

def extract_text_from_shape(shape):
    """从形状中提取文本"""
    text = ""
    if hasattr(shape, "text"):
        text = shape.text.strip()
    elif hasattr(shape, "text_frame"):
        text = shape.text_frame.text.strip()
    return text

def extract_slide_content(slide):
    """提取单张幻灯片的内容"""
    slide_data = {
        'title': '',
        'content': '',
        'notes': '',
        'images': [],
        'layout': 'standard'
    }
    
    texts = []
    title_found = False
    
    for shape in slide.shapes:
        # 提取文本
        if hasattr(shape, "text") or hasattr(shape, "text_frame"):
            text = extract_text_from_shape(shape)
            if text:
                # 第一个较长的文本通常是标题
                if not title_found and len(text) < 100:
                    slide_data['title'] = text
                    title_found = True
                else:
                    texts.append(text)
        
        # 检查图片
        elif shape.shape_type == MSO_SHAPE_TYPE.PICTURE:
            slide_data['images'].append({
                'name': getattr(shape, 'name', 'image'),
                'width': shape.width,
                'height': shape.height
            })
    
    # 合并内容文本
    slide_data['content'] = '\n'.join(texts)
    
    # 提取备注
    if hasattr(slide, 'notes_slide') and slide.notes_slide:
        try:
            notes_text = slide.notes_slide.notes_text_frame.text.strip()
            slide_data['notes'] = notes_text
        except:
            pass
    
    return slide_data

def extract_ppt_with_pptx(file_path):
    """使用python-pptx库提取PPT内容"""
    try:
        prs = Presentation(file_path)
        
        result = {
            'title': '',
            'slides': [],
            'metadata': {
                'slide_count': len(prs.slides),
                'slide_width': prs.slide_width,
                'slide_height': prs.slide_height
            }
        }
        
        for i, slide in enumerate(prs.slides):
            slide_data = extract_slide_content(slide)
            slide_data['slide_number'] = i + 1
            
            # 第一张幻灯片的标题作为整个PPT的标题
            if i == 0 and slide_data['title']:
                result['title'] = slide_data['title']
            
            result['slides'].append(slide_data)
        
        return result
        
    except Exception as e:
        raise Exception(f"PPT解析失败: {str(e)}")

def extract_ppt_basic(file_path):
    """基础PPT信息提取（当python-pptx不可用时）"""
    file_name = Path(file_path).stem
    file_size = os.path.getsize(file_path)
    
    return {
        'title': file_name,
        'slides': [
            {
                'slide_number': 1,
                'title': file_name,
                'content': '这是一个PPT模板文件',
                'notes': f'文件大小: {file_size} 字节',
                'images': [],
                'layout': 'title'
            },
            {
                'slide_number': 2,
                'title': '模板说明',
                'content': '此模板可用于生成新的PPT内容\n请在AI PPT生成器中输入您的内容',
                'notes': '模板使用说明',
                'images': [],
                'layout': 'content'
            }
        ],
        'metadata': {
            'slide_count': 2,
            'file_size': file_size,
            'extraction_method': 'basic'
        }
    }

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print(json.dumps({
            'error': '请提供PPT文件路径'
        }, ensure_ascii=False))
        sys.exit(1)
    
    file_path = sys.argv[1]
    
    # 检查文件是否存在
    if not os.path.exists(file_path):
        print(json.dumps({
            'error': f'文件不存在: {file_path}'
        }, ensure_ascii=False))
        sys.exit(1)
    
    # 检查文件扩展名
    file_ext = Path(file_path).suffix.lower()
    if file_ext not in ['.ppt', '.pptx']:
        print(json.dumps({
            'error': f'不支持的文件格式: {file_ext}'
        }, ensure_ascii=False))
        sys.exit(1)
    
    try:
        if HAS_PPTX and file_ext == '.pptx':
            # 使用python-pptx解析PPTX文件
            result = extract_ppt_with_pptx(file_path)
            result['extraction_method'] = 'python-pptx'
        else:
            # 使用基础方法
            result = extract_ppt_basic(file_path)
            result['extraction_method'] = 'basic'
        
        # 添加文件信息
        result['file_info'] = {
            'path': file_path,
            'name': Path(file_path).name,
            'size': os.path.getsize(file_path),
            'extension': file_ext
        }
        
        print(json.dumps(result, ensure_ascii=False, indent=2))
        
    except Exception as e:
        # 发生错误时返回基础信息
        error_result = extract_ppt_basic(file_path)
        error_result['error'] = str(e)
        error_result['extraction_method'] = 'fallback'
        
        print(json.dumps(error_result, ensure_ascii=False, indent=2))

if __name__ == '__main__':
    main()
