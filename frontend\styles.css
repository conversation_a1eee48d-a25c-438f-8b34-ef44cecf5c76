/* AI PPT Generator - 样式文件 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
.header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

.header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* 主要内容区域 */
.main-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 20px;
}

/* 输入区域 */
.input-section {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.input-container h2 {
    color: #4a5568;
    margin-bottom: 20px;
    font-size: 1.3rem;
}

#textInput {
    width: 100%;
    min-height: 200px;
    padding: 15px;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    font-size: 14px;
    line-height: 1.6;
    resize: vertical;
    transition: border-color 0.3s ease;
}

#textInput:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 模板选择 */
.template-selection {
    margin: 25px 0;
}

.template-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.template-header h3 {
    color: #4a5568;
    font-size: 1.1rem;
    margin: 0;
}

.template-actions {
    display: flex;
    gap: 8px;
}

.action-btn {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    padding: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #667eea;
}

.action-btn:hover {
    background: #eef2ff;
    border-color: #667eea;
}

.template-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
}

.template-card {
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    padding: 15px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #f8fafc;
}

.template-card:hover {
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.template-card.selected {
    border-color: #667eea;
    background: #eef2ff;
}

.template-card i {
    font-size: 2rem;
    color: #667eea;
    margin-bottom: 8px;
}

.template-card h4 {
    font-size: 0.9rem;
    color: #4a5568;
    margin-bottom: 5px;
}

.template-card p {
    font-size: 0.8rem;
    color: #718096;
    margin-bottom: 8px;
}

.template-card small {
    display: block;
    font-size: 0.7rem;
    color: #a0aec0;
    margin-top: 4px;
}

/* 模板统计信息 */
.template-stats {
    margin-top: 15px;
    padding: 15px;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.template-stats h4 {
    color: #4a5568;
    margin-bottom: 10px;
    font-size: 1rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
}

.stat-item {
    text-align: center;
    padding: 10px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
}

.stat-value {
    font-size: 1.2rem;
    font-weight: 600;
    color: #667eea;
}

.stat-label {
    font-size: 0.8rem;
    color: #718096;
    margin-top: 4px;
}

/* 选项设置 */
.options {
    margin: 25px 0;
}

.options h3 {
    color: #4a5568;
    margin-bottom: 15px;
    font-size: 1.1rem;
}

.option-group {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.option-group label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    font-size: 0.9rem;
    color: #4a5568;
}

.option-group input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: #667eea;
}

/* 生成按钮 */
.generate-btn {
    width: 100%;
    padding: 15px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 20px;
}

.generate-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.generate-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* 预览区域 */
.preview-section {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.preview-container h2 {
    color: #4a5568;
    margin-bottom: 20px;
    font-size: 1.3rem;
}

.preview-area {
    min-height: 400px;
    border: 2px dashed #e2e8f0;
    border-radius: 10px;
    background: #f8fafc;
    position: relative;
    overflow: hidden;
}

/* 预览标签页 */
.preview-tabs {
    display: flex;
    background: #e2e8f0;
    border-radius: 8px 8px 0 0;
    margin: -2px -2px 0 -2px;
}

.tab-btn {
    flex: 1;
    padding: 12px 20px;
    background: transparent;
    border: none;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    color: #718096;
    transition: all 0.3s ease;
    border-radius: 8px 8px 0 0;
}

.tab-btn.active {
    background: white;
    color: #667eea;
    font-weight: 600;
}

.tab-btn:hover:not(.active) {
    background: #f1f5f9;
    color: #4a5568;
}

.tab-content {
    display: none;
    height: calc(100% - 50px);
    padding: 20px;
}

.tab-content.active {
    display: flex;
    align-items: center;
    justify-content: center;
}

#pptPreview.tab-content.active {
    display: block;
    padding: 0;
}

.placeholder {
    text-align: center;
    color: #a0aec0;
}

.placeholder i {
    font-size: 3rem;
    margin-bottom: 15px;
}

.placeholder p {
    font-size: 1.1rem;
}

/* 加载指示器 */
.loading {
    text-align: center;
    color: #667eea;
}

.loading i {
    font-size: 2rem;
    margin-bottom: 15px;
}

.loading p {
    font-size: 1rem;
}

/* 幻灯片样式 */
.slide {
    width: 100%;
    height: 100%;
    padding: 30px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    display: none;
}

.slide.active {
    display: block;
}

.slide.title-slide {
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.slide.title-slide h1 {
    font-size: 2.5rem;
    color: #2d3748;
    margin-bottom: 20px;
}

.slide.title-slide h2 {
    font-size: 1.5rem;
    color: #667eea;
    font-weight: 400;
}

.slide.content-slide h1 {
    font-size: 2rem;
    color: #2d3748;
    margin-bottom: 25px;
    border-bottom: 3px solid #667eea;
    padding-bottom: 10px;
}

.slide.content-slide .content {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #4a5568;
}

.slide.content-slide .keywords {
    margin-top: 20px;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.keyword-tag {
    background: #eef2ff;
    color: #667eea;
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
}

/* 幻灯片导航 */
.slide-navigation {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
    margin-top: 20px;
}

.nav-btn {
    background: #667eea;
    color: white;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.nav-btn:hover {
    background: #5a67d8;
    transform: scale(1.1);
}

.nav-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

#slideCounter {
    font-weight: 600;
    color: #4a5568;
}

/* 导出选项 */
.export-options {
    margin-top: 25px;
    padding-top: 20px;
    border-top: 1px solid #e2e8f0;
}

.export-options h3 {
    color: #4a5568;
    margin-bottom: 15px;
    font-size: 1.1rem;
}

.export-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.export-btn {
    padding: 10px 15px;
    border: 2px solid #667eea;
    background: white;
    color: #667eea;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.export-btn:hover {
    background: #667eea;
    color: white;
}

/* 状态栏 */
.status-bar {
    background: rgba(255,255,255,0.9);
    padding: 10px 20px;
    border-radius: 10px;
    text-align: center;
    font-size: 0.9rem;
    color: #4a5568;
}

/* 工具类 */
.hidden {
    display: none !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .main-content {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .header h1 {
        font-size: 2rem;
    }

    .template-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .export-buttons {
        flex-direction: column;
    }
}
