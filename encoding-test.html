<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>编码测试 - AI PPT Generator</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .chinese-text {
            font-size: 16px;
            line-height: 1.6;
            color: #333;
        }
        .api-test {
            margin: 10px 0;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 3px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            border-left: 4px solid #28a745;
        }
        .error {
            border-left: 4px solid #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔤 中文编码测试页面</h1>
        
        <div class="test-section">
            <h2>📝 静态中文文本测试</h2>
            <div class="chinese-text">
                <p><strong>简体中文：</strong>人工智能PPT生成器</p>
                <p><strong>繁体中文：</strong>人工智慧PPT生成器</p>
                <p><strong>特殊字符：</strong>①②③④⑤ ★☆♠♥♦♣ ←→↑↓</p>
                <p><strong>标点符号：</strong>，。！？；：""''（）【】</p>
                <p><strong>数字混合：</strong>2023年销售额达到1000万元，同比增长20%</p>
            </div>
        </div>

        <div class="test-section">
            <h2>🌐 API响应编码测试</h2>
            <div class="api-test">
                <button onclick="testAPIEncoding()">测试API中文响应</button>
                <div id="apiResult" class="result"></div>
            </div>
        </div>

        <div class="test-section">
            <h2>📄 模板信息编码测试</h2>
            <div class="api-test">
                <button onclick="testTemplateEncoding()">测试模板中文信息</button>
                <div id="templateResult" class="result"></div>
            </div>
        </div>

        <div class="test-section">
            <h2>🧠 AI分析编码测试</h2>
            <div class="api-test">
                <button onclick="testAIEncoding()">测试AI中文分析</button>
                <div id="aiResult" class="result"></div>
            </div>
        </div>

        <div class="test-section">
            <h2>🔧 解决方案</h2>
            <p>如果您看到乱码，请尝试以下解决方案：</p>
            <ul>
                <li><strong>浏览器设置：</strong>确保浏览器编码设置为UTF-8</li>
                <li><strong>文件编码：</strong>确保所有HTML/CSS/JS文件保存为UTF-8编码</li>
                <li><strong>服务器配置：</strong>确保Express服务器正确设置Content-Type头</li>
                <li><strong>字体支持：</strong>确保系统安装了中文字体</li>
            </ul>
            
            <button onclick="openMainApp()">🏠 返回主应用</button>
            <button onclick="reloadPage()">🔄 刷新页面</button>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3000/api';

        async function testAPIEncoding() {
            const resultDiv = document.getElementById('apiResult');
            resultDiv.textContent = '正在测试API编码...';
            
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                
                resultDiv.className = 'result success';
                resultDiv.textContent = `✅ API响应正常
响应内容: ${JSON.stringify(data, null, 2)}
编码状态: ${response.headers.get('content-type') || '未知'}`;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ API测试失败: ${error.message}`;
            }
        }

        async function testTemplateEncoding() {
            const resultDiv = document.getElementById('templateResult');
            resultDiv.textContent = '正在测试模板编码...';
            
            try {
                const response = await fetch(`${API_BASE}/templates`);
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 模板信息编码正常
模板数量: ${data.templates.length}
模板详情:
${data.templates.map(t => `- ${t.name}: ${t.description}`).join('\n')}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 模板加载失败: ${data.error}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 模板测试失败: ${error.message}`;
            }
        }

        async function testAIEncoding() {
            const resultDiv = document.getElementById('aiResult');
            resultDiv.textContent = '正在测试AI分析编码...';
            
            const testText = `测试中文内容
这是一个编码测试

一、中文标题
包含中文的内容，测试编码是否正确

二、特殊字符
包含特殊字符：①②③ ★☆ ""''`;

            try {
                const response = await fetch(`${API_BASE}/generate-ppt`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json; charset=utf-8'
                    },
                    body: JSON.stringify({
                        text: testText,
                        templateId: 1,
                        options: { autoLayout: true }
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ AI分析编码正常
生成幻灯片: ${data.slides.length} 张
模板名称: ${data.metadata.template.name}
AI分析结果:
${data.slides.map((slide, index) => `${index + 1}. ${slide.title}`).join('\n')}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ AI分析失败: ${data.error}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ AI测试失败: ${error.message}`;
            }
        }

        function openMainApp() {
            window.open('http://localhost:3000', '_blank');
        }

        function reloadPage() {
            window.location.reload();
        }

        // 页面加载时显示编码信息
        window.onload = function() {
            console.log('页面编码信息:');
            console.log('- 文档字符集:', document.characterSet);
            console.log('- 文档编码:', document.inputEncoding);
            console.log('- 浏览器语言:', navigator.language);
            console.log('- 用户代理:', navigator.userAgent);
        };
    </script>
</body>
</html>
