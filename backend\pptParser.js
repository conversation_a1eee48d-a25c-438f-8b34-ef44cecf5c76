// PPT文件解析器
const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

class PPTParser {
    constructor() {
        this.supportedFormats = ['.pptx', '.ppt'];
    }

    // 检查文件是否为支持的PPT格式
    isSupportedFormat(filePath) {
        const ext = path.extname(filePath).toLowerCase();
        return this.supportedFormats.includes(ext);
    }

    // 解析PPT文件并提取内容
    async parsePPTFile(filePath) {
        try {
            if (!fs.existsSync(filePath)) {
                throw new Error('PPT文件不存在');
            }

            if (!this.isSupportedFormat(filePath)) {
                throw new Error('不支持的文件格式');
            }

            console.log(`📄 开始解析PPT文件: ${filePath}`);

            // 使用Python脚本解析PPT文件
            const pptData = await this.extractPPTContent(filePath);
            
            // 转换为标准格式
            const slides = this.convertToStandardFormat(pptData);
            
            return {
                success: true,
                filePath: filePath,
                fileName: path.basename(filePath),
                slideCount: slides.length,
                slides: slides,
                metadata: {
                    title: slides[0]?.title || path.parse(filePath).name,
                    author: 'PPT模板',
                    createdAt: fs.statSync(filePath).birthtime,
                    modifiedAt: fs.statSync(filePath).mtime,
                    fileSize: fs.statSync(filePath).size
                }
            };

        } catch (error) {
            console.error('PPT解析错误:', error);
            return {
                success: false,
                error: error.message,
                slides: this.createFallbackSlides(filePath)
            };
        }
    }

    // 使用Python脚本提取PPT内容
    extractPPTContent(filePath) {
        return new Promise((resolve, reject) => {
            const pythonScript = path.join(__dirname, '../models/ppt_extractor.py');
            
            const pythonProcess = spawn('python', [pythonScript, filePath], {
                encoding: 'utf8',
                env: { ...process.env, PYTHONIOENCODING: 'utf-8' }
            });

            let result = '';
            let error = '';

            pythonProcess.stdout.on('data', (data) => {
                result += data.toString();
            });

            pythonProcess.stderr.on('data', (data) => {
                error += data.toString();
            });

            pythonProcess.on('close', (code) => {
                if (code === 0) {
                    try {
                        const parsedResult = JSON.parse(result);
                        resolve(parsedResult);
                    } catch (e) {
                        console.warn('Python脚本返回数据解析失败，使用基础解析');
                        resolve(this.basicPPTAnalysis(filePath));
                    }
                } else {
                    console.warn(`Python PPT解析失败 (${code}): ${error}`);
                    resolve(this.basicPPTAnalysis(filePath));
                }
            });
        });
    }

    // 基础PPT分析（当Python脚本不可用时）
    basicPPTAnalysis(filePath) {
        const fileName = path.parse(filePath).name;
        const stats = fs.statSync(filePath);
        
        return {
            title: fileName,
            slides: [
                {
                    title: fileName,
                    content: '这是一个PPT模板文件',
                    type: 'title'
                },
                {
                    title: '模板信息',
                    content: `文件名: ${path.basename(filePath)}\n文件大小: ${this.formatFileSize(stats.size)}\n创建时间: ${stats.birthtime.toLocaleString('zh-CN')}`,
                    type: 'content'
                },
                {
                    title: '使用说明',
                    content: '此模板可用于生成新的PPT内容\n请在输入框中输入您的内容\n点击生成按钮创建演示文稿',
                    type: 'content'
                }
            ]
        };
    }

    // 转换为标准幻灯片格式
    convertToStandardFormat(pptData) {
        if (!pptData || !pptData.slides) {
            return this.createDefaultSlides(pptData?.title || '未知模板');
        }

        return pptData.slides.map((slide, index) => ({
            id: index + 1,
            type: this.determineSlideType(slide, index),
            title: slide.title || `幻灯片 ${index + 1}`,
            subtitle: slide.subtitle || '',
            content: slide.content || slide.text || '',
            notes: slide.notes || '',
            image: slide.image || null,
            keywords: this.extractKeywords(slide.content || slide.text || ''),
            layout: slide.layout || 'standard',
            background: slide.background || null
        }));
    }

    // 确定幻灯片类型
    determineSlideType(slide, index) {
        if (index === 0) return 'title';
        if (slide.type) return slide.type;
        if (slide.title && slide.title.includes('总结')) return 'summary';
        if (slide.image) return 'image';
        return 'content';
    }

    // 提取关键词
    extractKeywords(text) {
        if (!text) return [];
        
        // 简单的关键词提取
        const words = text.split(/\s+/)
            .filter(word => word.length > 1)
            .slice(0, 5);
        
        return words;
    }

    // 创建默认幻灯片
    createDefaultSlides(title) {
        return [
            {
                id: 1,
                type: 'title',
                title: title || 'PPT模板',
                subtitle: '模板预览',
                content: '',
                keywords: []
            },
            {
                id: 2,
                type: 'content',
                title: '模板说明',
                content: '这是一个PPT模板文件\n您可以基于此模板生成新的演示内容',
                keywords: ['模板', '演示']
            }
        ];
    }

    // 创建备用幻灯片（解析失败时）
    createFallbackSlides(filePath) {
        const fileName = path.parse(filePath).name;
        
        return [
            {
                id: 1,
                type: 'title',
                title: fileName,
                subtitle: 'PPT模板文件',
                content: '',
                keywords: []
            },
            {
                id: 2,
                type: 'content',
                title: '模板信息',
                content: `文件: ${path.basename(filePath)}\n状态: 可用于生成新内容\n说明: 请输入您的内容来使用此模板`,
                keywords: ['模板', '文件']
            }
        ];
    }

    // 格式化文件大小
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // 获取PPT文件的缩略图（如果可能）
    async generateThumbnail(filePath) {
        try {
            // 这里可以实现PPT缩略图生成
            // 目前返回占位符
            return {
                success: false,
                message: '缩略图生成功能开发中'
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }
}

module.exports = PPTParser;
