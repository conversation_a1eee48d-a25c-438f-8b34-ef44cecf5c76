<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PPT预览测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 3px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            border-left: 4px solid #28a745;
        }
        .error {
            border-left: 4px solid #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 PPT模板预览功能测试</h1>
        
        <div class="test-section">
            <h2>1. 获取模板列表</h2>
            <button onclick="testGetTemplates()">获取模板</button>
            <div id="templatesResult" class="result"></div>
        </div>

        <div class="test-section">
            <h2>2. 测试模板预览</h2>
            <button onclick="testTemplatePreview(1)">预览模板 1</button>
            <div id="previewResult" class="result"></div>
        </div>

        <div class="test-section">
            <h2>3. 测试Python PPT解析</h2>
            <button onclick="testPythonExtractor()">测试Python解析器</button>
            <div id="pythonResult" class="result"></div>
        </div>

        <div class="test-section">
            <h2>4. 完整功能测试</h2>
            <button onclick="runFullTest()">运行完整测试</button>
            <div id="fullTestResult" class="result"></div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/axios/1.6.0/axios.min.js"></script>
    <script>
        async function testGetTemplates() {
            const resultDiv = document.getElementById('templatesResult');
            resultDiv.textContent = '正在获取模板列表...';
            
            try {
                const response = await axios.get('/api/templates');
                
                if (response.data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 成功获取 ${response.data.templates.length} 个模板：\n\n${JSON.stringify(response.data, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 获取失败: ${response.data.error}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 请求失败: ${error.message}`;
            }
        }

        async function testTemplatePreview(templateId) {
            const resultDiv = document.getElementById('previewResult');
            resultDiv.textContent = `正在预览模板 ${templateId}...`;
            
            try {
                const response = await axios.get(`/api/templates/${templateId}/preview`);
                
                if (response.data.success) {
                    resultDiv.className = 'result success';
                    
                    const slideCount = response.data.slides ? response.data.slides.length : 0;
                    const method = response.data.extraction_method || '未知';
                    
                    resultDiv.textContent = `✅ 模板预览成功！
                    
模板信息:
- 名称: ${response.data.template.name}
- 文件: ${response.data.template.filename || '默认模板'}
- 幻灯片数量: ${slideCount}
- 解析方式: ${method}
- 是否默认模板: ${response.data.isDefault ? '是' : '否'}

幻灯片内容:
${response.data.slides ? response.data.slides.map((slide, i) => 
    `${i + 1}. ${slide.title} (${slide.type})`
).join('\n') : '无幻灯片数据'}

完整响应:
${JSON.stringify(response.data, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 预览失败: ${response.data.error}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 请求失败: ${error.message}`;
            }
        }

        async function testPythonExtractor() {
            const resultDiv = document.getElementById('pythonResult');
            resultDiv.textContent = '测试Python PPT解析器...';
            
            // 这个测试通过模板预览API间接测试Python解析器
            try {
                const templatesResponse = await axios.get('/api/templates');
                
                if (templatesResponse.data.success && templatesResponse.data.templates.length > 0) {
                    const template = templatesResponse.data.templates[0];
                    
                    if (!template.isDefault) {
                        const previewResponse = await axios.get(`/api/templates/${template.id}/preview`);
                        
                        resultDiv.className = 'result success';
                        resultDiv.textContent = `✅ Python解析器测试完成！
                        
测试文件: ${template.filename}
解析结果: ${previewResponse.data.success ? '成功' : '失败'}
解析方式: ${previewResponse.data.extraction_method || '未知'}
错误信息: ${previewResponse.data.error || '无'}

详细信息:
${JSON.stringify(previewResponse.data, null, 2)}`;
                    } else {
                        resultDiv.className = 'result success';
                        resultDiv.textContent = '✅ 当前只有默认模板，Python解析器未被调用';
                    }
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = '❌ 无法获取模板列表进行测试';
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 测试失败: ${error.message}`;
            }
        }

        async function runFullTest() {
            const resultDiv = document.getElementById('fullTestResult');
            resultDiv.textContent = '开始运行完整测试...\n';
            
            const tests = [
                { name: '获取模板列表', func: () => axios.get('/api/templates') },
                { name: '健康检查', func: () => axios.get('/api/health') },
                { name: '模板预览', func: () => axios.get('/api/templates/1/preview') }
            ];
            
            let results = [];
            
            for (const test of tests) {
                try {
                    const startTime = Date.now();
                    const response = await test.func();
                    const duration = Date.now() - startTime;
                    
                    results.push(`✅ ${test.name}: 成功 (${duration}ms)`);
                    
                    if (test.name === '模板预览' && response.data.slides) {
                        results.push(`   - 幻灯片数量: ${response.data.slides.length}`);
                        results.push(`   - 解析方式: ${response.data.extraction_method || '未知'}`);
                    }
                } catch (error) {
                    results.push(`❌ ${test.name}: 失败 - ${error.message}`);
                }
            }
            
            resultDiv.className = 'result success';
            resultDiv.textContent = `完整测试结果:\n\n${results.join('\n')}\n\n测试完成时间: ${new Date().toLocaleString()}`;
        }

        // 页面加载时自动运行基础测试
        window.addEventListener('load', () => {
            console.log('页面加载完成，可以开始测试');
            setTimeout(() => {
                testGetTemplates();
            }, 1000);
        });
    </script>
</body>
</html>
