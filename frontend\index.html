<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI PPT 生成器</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <header class="header">
            <h1><i class="fas fa-magic"></i> AI PPT 生成器</h1>
            <p>智能分析您的内容，自动生成专业演示文稿</p>
        </header>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 输入区域 -->
            <section class="input-section">
                <div class="input-container">
                    <h2><i class="fas fa-edit"></i> 输入内容</h2>
                    <textarea
                        id="textInput"
                        placeholder="请输入您要制作PPT的内容...&#10;&#10;例如：&#10;公司年度总结报告&#10;2023年业绩回顾&#10;&#10;一、销售业绩&#10;今年销售额达到1000万元，同比增长20%&#10;&#10;二、市场拓展&#10;新开拓了5个城市市场，客户满意度达到95%&#10;&#10;三、未来规划&#10;计划明年继续扩大市场份额，目标增长30%"
                        rows="10"
                    ></textarea>

                    <div class="template-selection">
                        <div class="template-header">
                            <h3><i class="fas fa-palette"></i> 选择模板</h3>
                            <div class="template-actions">
                                <button id="reloadTemplatesBtn" class="action-btn" title="重新加载模板">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                                <button id="templateInfoBtn" class="action-btn" title="模板信息">
                                    <i class="fas fa-info-circle"></i>
                                </button>
                            </div>
                        </div>
                        <div class="template-grid" id="templateGrid">
                            <!-- 模板选项将通过JavaScript动态加载 -->
                        </div>
                        <div id="templateStats" class="template-stats hidden">
                            <!-- 模板统计信息 -->
                        </div>
                    </div>

                    <div class="options">
                        <h3><i class="fas fa-cog"></i> 生成选项</h3>
                        <div class="option-group">
                            <label>
                                <input type="checkbox" id="autoLayout" checked>
                                自动布局优化
                            </label>
                            <label>
                                <input type="checkbox" id="includeImages" checked>
                                包含图片建议
                            </label>
                            <label>
                                <input type="checkbox" id="addTransitions">
                                添加过渡效果
                            </label>
                        </div>
                    </div>

                    <button id="generateBtn" class="generate-btn">
                        <i class="fas fa-wand-magic-sparkles"></i>
                        生成 PPT
                    </button>
                </div>
            </section>

            <!-- 预览区域 -->
            <section class="preview-section">
                <div class="preview-container">
                    <h2><i class="fas fa-eye"></i> 预览</h2>
                    <div id="loadingIndicator" class="loading hidden">
                        <i class="fas fa-spinner fa-spin"></i>
                        <p>AI正在分析您的内容并生成PPT...</p>
                    </div>

                    <div id="previewArea" class="preview-area">
                        <div class="placeholder">
                            <i class="fas fa-presentation-screen"></i>
                            <p>生成的PPT预览将在这里显示</p>
                        </div>
                    </div>

                    <div id="slideNavigation" class="slide-navigation hidden">
                        <button id="prevSlide" class="nav-btn">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <span id="slideCounter">1 / 1</span>
                        <button id="nextSlide" class="nav-btn">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>

                    <div id="exportOptions" class="export-options hidden">
                        <h3><i class="fas fa-download"></i> 导出选项</h3>
                        <div class="export-buttons">
                            <button class="export-btn" data-format="pptx">
                                <i class="fas fa-file-powerpoint"></i>
                                PowerPoint
                            </button>
                            <button class="export-btn" data-format="pdf">
                                <i class="fas fa-file-pdf"></i>
                                PDF
                            </button>
                            <button class="export-btn" data-format="html">
                                <i class="fas fa-globe"></i>
                                网页版
                            </button>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <!-- 状态栏 -->
        <div id="statusBar" class="status-bar">
            <span id="statusText">就绪</span>
        </div>
    </div>

    <!-- 模态框 -->
    <div id="modal" class="modal hidden">
        <div class="modal-content">
            <span class="close">&times;</span>
            <div id="modalBody"></div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/axios/1.6.0/axios.min.js"></script>
    <script src="app.js"></script>
</body>
</html>
