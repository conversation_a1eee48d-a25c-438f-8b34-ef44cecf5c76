// PPT Web预览器
class PPTViewer {
    constructor(containerId) {
        this.container = document.getElementById(containerId);
        this.currentSlide = 0;
        this.slides = [];
        this.isFullscreen = false;
        this.autoPlay = false;
        this.autoPlayInterval = null;
        this.init();
    }

    init() {
        this.createViewerHTML();
        this.bindEvents();
    }

    createViewerHTML() {
        this.container.innerHTML = `
            <div class="ppt-viewer">
                <div class="ppt-toolbar">
                    <div class="toolbar-left">
                        <button class="btn-icon" id="prevSlideBtn" title="上一张">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <span class="slide-counter" id="slideCounter">1 / 1</span>
                        <button class="btn-icon" id="nextSlideBtn" title="下一张">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                    
                    <div class="toolbar-center">
                        <button class="btn-icon" id="playBtn" title="自动播放">
                            <i class="fas fa-play"></i>
                        </button>
                        <button class="btn-icon" id="stopBtn" title="停止播放" style="display:none;">
                            <i class="fas fa-stop"></i>
                        </button>
                        <select id="speedSelect" class="speed-select">
                            <option value="3000">慢速 (3s)</option>
                            <option value="2000" selected>正常 (2s)</option>
                            <option value="1000">快速 (1s)</option>
                        </select>
                    </div>
                    
                    <div class="toolbar-right">
                        <button class="btn-icon" id="thumbnailBtn" title="缩略图">
                            <i class="fas fa-th"></i>
                        </button>
                        <button class="btn-icon" id="fullscreenBtn" title="全屏">
                            <i class="fas fa-expand"></i>
                        </button>
                        <button class="btn-icon" id="downloadBtn" title="下载PPT">
                            <i class="fas fa-download"></i>
                        </button>
                    </div>
                </div>
                
                <div class="ppt-main">
                    <div class="slide-container" id="slideContainer">
                        <div class="slide-placeholder">
                            <i class="fas fa-file-powerpoint"></i>
                            <p>请加载PPT文件进行预览</p>
                        </div>
                    </div>
                    
                    <div class="thumbnail-panel" id="thumbnailPanel" style="display:none;">
                        <div class="thumbnail-grid" id="thumbnailGrid">
                            <!-- 缩略图将在这里显示 -->
                        </div>
                    </div>
                </div>
                
                <div class="slide-notes" id="slideNotes" style="display:none;">
                    <h4>幻灯片备注</h4>
                    <div class="notes-content" id="notesContent">
                        <!-- 备注内容 -->
                    </div>
                </div>
            </div>
        `;
    }

    bindEvents() {
        // 导航按钮
        document.getElementById('prevSlideBtn').addEventListener('click', () => this.previousSlide());
        document.getElementById('nextSlideBtn').addEventListener('click', () => this.nextSlide());
        
        // 播放控制
        document.getElementById('playBtn').addEventListener('click', () => this.startAutoPlay());
        document.getElementById('stopBtn').addEventListener('click', () => this.stopAutoPlay());
        
        // 工具栏功能
        document.getElementById('thumbnailBtn').addEventListener('click', () => this.toggleThumbnails());
        document.getElementById('fullscreenBtn').addEventListener('click', () => this.toggleFullscreen());
        document.getElementById('downloadBtn').addEventListener('click', () => this.downloadPPT());
        
        // 键盘快捷键
        document.addEventListener('keydown', (e) => this.handleKeyboard(e));
        
        // 鼠标滚轮
        this.container.addEventListener('wheel', (e) => this.handleWheel(e));
    }

    // 加载PPT数据
    loadPPT(pptData) {
        this.slides = pptData.slides || [];
        this.currentSlide = 0;
        this.renderSlides();
        this.updateNavigation();
        this.generateThumbnails();
    }

    // 渲染幻灯片
    renderSlides() {
        const container = document.getElementById('slideContainer');
        
        if (this.slides.length === 0) {
            container.innerHTML = `
                <div class="slide-placeholder">
                    <i class="fas fa-file-powerpoint"></i>
                    <p>没有可显示的幻灯片</p>
                </div>
            `;
            return;
        }

        container.innerHTML = '';
        
        this.slides.forEach((slide, index) => {
            const slideElement = document.createElement('div');
            slideElement.className = `slide ${index === this.currentSlide ? 'active' : ''}`;
            slideElement.dataset.slideIndex = index;
            
            slideElement.innerHTML = this.generateSlideHTML(slide);
            container.appendChild(slideElement);
        });
    }

    // 生成幻灯片HTML
    generateSlideHTML(slide) {
        const slideClass = `slide-${slide.type || 'content'}`;
        
        switch (slide.type) {
            case 'title':
                return `
                    <div class="${slideClass}">
                        <div class="slide-content">
                            <h1 class="slide-title">${slide.title || ''}</h1>
                            <h2 class="slide-subtitle">${slide.subtitle || ''}</h2>
                            ${slide.author ? `<p class="slide-author">${slide.author}</p>` : ''}
                            ${slide.date ? `<p class="slide-date">${slide.date}</p>` : ''}
                        </div>
                    </div>
                `;
                
            case 'content':
                return `
                    <div class="${slideClass}">
                        <div class="slide-content">
                            <h1 class="slide-title">${slide.title || ''}</h1>
                            <div class="slide-body">
                                ${this.formatContent(slide.content || '')}
                            </div>
                            ${this.generateKeywordTags(slide.keywords)}
                            ${slide.image ? `<div class="slide-image"><img src="${slide.image}" alt="幻灯片图片"></div>` : ''}
                        </div>
                    </div>
                `;
                
            case 'summary':
                return `
                    <div class="${slideClass}">
                        <div class="slide-content">
                            <h1 class="slide-title">${slide.title || '总结'}</h1>
                            <div class="summary-content">
                                ${this.formatContent(slide.content || '')}
                            </div>
                            ${this.generateKeywordTags(slide.keywords)}
                        </div>
                    </div>
                `;
                
            case 'image':
                return `
                    <div class="${slideClass}">
                        <div class="slide-content">
                            ${slide.title ? `<h1 class="slide-title">${slide.title}</h1>` : ''}
                            <div class="image-container">
                                <img src="${slide.image}" alt="${slide.title || '图片'}">
                                ${slide.caption ? `<p class="image-caption">${slide.caption}</p>` : ''}
                            </div>
                        </div>
                    </div>
                `;
                
            default:
                return `
                    <div class="${slideClass}">
                        <div class="slide-content">
                            <h1 class="slide-title">${slide.title || '幻灯片'}</h1>
                            <div class="slide-body">
                                ${this.formatContent(slide.content || '')}
                            </div>
                        </div>
                    </div>
                `;
        }
    }

    // 格式化内容
    formatContent(content) {
        if (!content) return '';
        
        // 处理换行
        let formatted = content.replace(/\n/g, '<br>');
        
        // 处理列表
        formatted = formatted.replace(/^[\s]*[-•]\s*(.+)$/gm, '<li>$1</li>');
        formatted = formatted.replace(/(<li>.*<\/li>)/s, '<ul>$1</ul>');
        
        // 处理编号列表
        formatted = formatted.replace(/^[\s]*\d+\.\s*(.+)$/gm, '<li>$1</li>');
        
        return formatted;
    }

    // 生成关键词标签
    generateKeywordTags(keywords) {
        if (!keywords || keywords.length === 0) return '';
        
        return `
            <div class="keyword-tags">
                ${keywords.map(keyword => `<span class="keyword-tag">${keyword}</span>`).join('')}
            </div>
        `;
    }

    // 生成缩略图
    generateThumbnails() {
        const grid = document.getElementById('thumbnailGrid');
        grid.innerHTML = '';
        
        this.slides.forEach((slide, index) => {
            const thumbnail = document.createElement('div');
            thumbnail.className = `thumbnail ${index === this.currentSlide ? 'active' : ''}`;
            thumbnail.dataset.slideIndex = index;
            
            thumbnail.innerHTML = `
                <div class="thumbnail-content">
                    <div class="thumbnail-slide">
                        ${this.generateSlideHTML(slide)}
                    </div>
                    <div class="thumbnail-info">
                        <span class="thumbnail-number">${index + 1}</span>
                        <span class="thumbnail-title">${slide.title || `幻灯片 ${index + 1}`}</span>
                    </div>
                </div>
            `;
            
            thumbnail.addEventListener('click', () => this.goToSlide(index));
            grid.appendChild(thumbnail);
        });
    }

    // 导航方法
    previousSlide() {
        if (this.currentSlide > 0) {
            this.goToSlide(this.currentSlide - 1);
        }
    }

    nextSlide() {
        if (this.currentSlide < this.slides.length - 1) {
            this.goToSlide(this.currentSlide + 1);
        }
    }

    goToSlide(index) {
        if (index >= 0 && index < this.slides.length) {
            this.currentSlide = index;
            this.updateSlideDisplay();
            this.updateNavigation();
            this.updateThumbnails();
        }
    }

    updateSlideDisplay() {
        const slides = document.querySelectorAll('.slide');
        slides.forEach((slide, index) => {
            slide.classList.toggle('active', index === this.currentSlide);
        });
    }

    updateNavigation() {
        const counter = document.getElementById('slideCounter');
        const prevBtn = document.getElementById('prevSlideBtn');
        const nextBtn = document.getElementById('nextSlideBtn');
        
        counter.textContent = `${this.currentSlide + 1} / ${this.slides.length}`;
        prevBtn.disabled = this.currentSlide === 0;
        nextBtn.disabled = this.currentSlide === this.slides.length - 1;
    }

    updateThumbnails() {
        const thumbnails = document.querySelectorAll('.thumbnail');
        thumbnails.forEach((thumb, index) => {
            thumb.classList.toggle('active', index === this.currentSlide);
        });
    }

    // 自动播放
    startAutoPlay() {
        const speed = parseInt(document.getElementById('speedSelect').value);
        this.autoPlay = true;
        
        document.getElementById('playBtn').style.display = 'none';
        document.getElementById('stopBtn').style.display = 'inline-block';
        
        this.autoPlayInterval = setInterval(() => {
            if (this.currentSlide < this.slides.length - 1) {
                this.nextSlide();
            } else {
                this.stopAutoPlay();
            }
        }, speed);
    }

    stopAutoPlay() {
        this.autoPlay = false;
        
        if (this.autoPlayInterval) {
            clearInterval(this.autoPlayInterval);
            this.autoPlayInterval = null;
        }
        
        document.getElementById('playBtn').style.display = 'inline-block';
        document.getElementById('stopBtn').style.display = 'none';
    }

    // 工具栏功能
    toggleThumbnails() {
        const panel = document.getElementById('thumbnailPanel');
        const isVisible = panel.style.display !== 'none';
        panel.style.display = isVisible ? 'none' : 'block';
    }

    toggleFullscreen() {
        if (!this.isFullscreen) {
            if (this.container.requestFullscreen) {
                this.container.requestFullscreen();
            }
        } else {
            if (document.exitFullscreen) {
                document.exitFullscreen();
            }
        }
        this.isFullscreen = !this.isFullscreen;
    }

    downloadPPT() {
        // 这里可以实现PPT下载功能
        alert('PPT下载功能开发中...\n\n当前支持的操作：\n1. 复制幻灯片内容\n2. 截图保存\n3. 打印为PDF');
    }

    // 事件处理
    handleKeyboard(e) {
        if (!this.container.contains(e.target)) return;
        
        switch (e.key) {
            case 'ArrowLeft':
            case 'ArrowUp':
                e.preventDefault();
                this.previousSlide();
                break;
            case 'ArrowRight':
            case 'ArrowDown':
            case ' ':
                e.preventDefault();
                this.nextSlide();
                break;
            case 'Home':
                e.preventDefault();
                this.goToSlide(0);
                break;
            case 'End':
                e.preventDefault();
                this.goToSlide(this.slides.length - 1);
                break;
            case 'Escape':
                if (this.isFullscreen) {
                    this.toggleFullscreen();
                }
                break;
        }
    }

    handleWheel(e) {
        e.preventDefault();
        if (e.deltaY > 0) {
            this.nextSlide();
        } else {
            this.previousSlide();
        }
    }
}

// 导出PPTViewer类
window.PPTViewer = PPTViewer;
