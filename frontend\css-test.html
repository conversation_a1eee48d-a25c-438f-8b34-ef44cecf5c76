<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSS加载测试</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        .test-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            z-index: 9999;
        }
        .fallback-styles {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background: #f0f0f0;
            border: 1px solid #ccc;
        }
        .fallback-styles h1 {
            color: #333;
            margin-bottom: 20px;
        }
        .fallback-styles .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="test-info" id="testInfo">
        CSS状态: 检查中...
    </div>

    <!-- 使用主样式的元素 -->
    <div class="container">
        <header class="header">
            <h1>CSS加载测试页面</h1>
            <p>如果您看到漂亮的渐变背景和样式，说明CSS已正确加载</p>
        </header>

        <main class="main-content">
            <section class="input-section">
                <div class="input-container">
                    <h2>测试区域 1</h2>
                    <p>这个区域应该有白色背景和圆角边框</p>
                    
                    <div class="template-grid">
                        <div class="template-card selected">
                            <h4>测试卡片 1</h4>
                            <p>选中状态</p>
                        </div>
                        <div class="template-card">
                            <h4>测试卡片 2</h4>
                            <p>普通状态</p>
                        </div>
                    </div>
                    
                    <button class="generate-btn">测试按钮</button>
                </div>
            </section>

            <section class="preview-section">
                <div class="preview-container">
                    <h2>测试区域 2</h2>
                    <div class="preview-area">
                        <div class="placeholder">
                            <p>这里应该有虚线边框</p>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- 备用样式（如果主CSS未加载） -->
    <div class="fallback-styles" id="fallbackContent" style="display: none;">
        <h1>⚠️ CSS样式未正确加载</h1>
        <div class="warning">
            <strong>问题：</strong>主样式文件 (styles.css) 未能正确加载。
        </div>
        
        <h2>可能的原因：</h2>
        <ul>
            <li>服务器未正确配置静态文件服务</li>
            <li>CSS文件路径错误</li>
            <li>MIME类型配置问题</li>
            <li>缓存问题</li>
        </ul>
        
        <h2>解决方案：</h2>
        <ol>
            <li>检查服务器控制台是否有错误信息</li>
            <li>确认 styles.css 文件存在于 frontend 目录</li>
            <li>检查浏览器开发者工具的网络标签</li>
            <li>尝试直接访问: <a href="/styles.css" target="_blank">/styles.css</a></li>
            <li>清除浏览器缓存后刷新</li>
        </ol>
        
        <button onclick="location.reload()">🔄 刷新页面</button>
        <button onclick="window.open('/debug.html', '_blank')">🔍 打开调试页面</button>
        <button onclick="testDirectCSS()">📄 直接测试CSS</button>
    </div>

    <script>
        function updateTestInfo(message, color = 'white') {
            const testInfo = document.getElementById('testInfo');
            testInfo.textContent = message;
            testInfo.style.color = color;
        }

        function checkCSSLoaded() {
            // 检查是否应用了主样式
            const body = document.body;
            const computedStyle = window.getComputedStyle(body);
            
            // 检查背景是否为渐变（主样式的特征）
            const background = computedStyle.background || computedStyle.backgroundImage;
            const hasGradient = background.includes('linear-gradient') || background.includes('gradient');
            
            // 检查字体
            const fontFamily = computedStyle.fontFamily;
            const hasCustomFont = fontFamily.includes('Segoe UI') || fontFamily.includes('Tahoma');
            
            // 检查特定元素的样式
            const container = document.querySelector('.container');
            let hasContainerStyles = false;
            if (container) {
                const containerStyle = window.getComputedStyle(container);
                hasContainerStyles = containerStyle.maxWidth !== 'none' && containerStyle.maxWidth !== '';
            }
            
            const cssLoaded = hasGradient || hasCustomFont || hasContainerStyles;
            
            if (cssLoaded) {
                updateTestInfo('✅ CSS已正确加载', '#4CAF50');
                console.log('CSS加载成功');
                console.log('背景:', background);
                console.log('字体:', fontFamily);
            } else {
                updateTestInfo('❌ CSS未加载', '#F44336');
                console.log('CSS加载失败');
                console.log('背景:', background);
                console.log('字体:', fontFamily);
                
                // 显示备用内容
                document.querySelector('.container').style.display = 'none';
                document.getElementById('fallbackContent').style.display = 'block';
            }
            
            return cssLoaded;
        }

        function testDirectCSS() {
            updateTestInfo('🔄 测试直接CSS访问...', '#FF9800');
            
            fetch('/styles.css')
                .then(response => {
                    if (response.ok) {
                        return response.text();
                    } else {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                })
                .then(cssText => {
                    console.log('CSS文件内容长度:', cssText.length);
                    console.log('CSS文件开头:', cssText.substring(0, 200));
                    
                    if (cssText.length > 0) {
                        updateTestInfo('✅ CSS文件可访问', '#4CAF50');
                        
                        // 尝试重新加载CSS
                        const link = document.createElement('link');
                        link.rel = 'stylesheet';
                        link.href = '/styles.css?t=' + Date.now();
                        link.onload = () => {
                            updateTestInfo('✅ CSS重新加载成功', '#4CAF50');
                            setTimeout(() => {
                                location.reload();
                            }, 1000);
                        };
                        link.onerror = () => {
                            updateTestInfo('❌ CSS重新加载失败', '#F44336');
                        };
                        document.head.appendChild(link);
                    } else {
                        updateTestInfo('❌ CSS文件为空', '#F44336');
                    }
                })
                .catch(error => {
                    console.error('CSS访问错误:', error);
                    updateTestInfo(`❌ CSS访问失败: ${error.message}`, '#F44336');
                });
        }

        // 页面加载完成后检查
        window.addEventListener('load', () => {
            setTimeout(checkCSSLoaded, 500);
        });

        // 监听CSS加载事件
        document.addEventListener('DOMContentLoaded', () => {
            const links = document.querySelectorAll('link[rel="stylesheet"]');
            links.forEach(link => {
                link.addEventListener('load', () => {
                    console.log('CSS文件加载完成:', link.href);
                });
                link.addEventListener('error', () => {
                    console.error('CSS文件加载失败:', link.href);
                });
            });
        });

        // 输出调试信息
        console.log('当前页面URL:', window.location.href);
        console.log('CSS链接:', Array.from(document.querySelectorAll('link[rel="stylesheet"]')).map(l => l.href));
    </script>
</body>
</html>
