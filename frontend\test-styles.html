<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>样式测试 - AI PPT Generator</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="ppt-viewer.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .test-container {
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-warning { background: #ffc107; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1><i class="fas fa-palette"></i> 样式加载测试</h1>
        
        <div class="test-section">
            <h2>CSS文件加载状态</h2>
            <div id="cssStatus">
                <p><span class="status-indicator" id="mainCssStatus"></span>主样式文件 (styles.css)</p>
                <p><span class="status-indicator" id="pptCssStatus"></span>PPT预览样式 (ppt-viewer.css)</p>
                <p><span class="status-indicator" id="fontAwesomeStatus"></span>Font Awesome 图标</p>
            </div>
        </div>

        <div class="test-section">
            <h2>样式组件测试</h2>
            
            <h3>按钮样式</h3>
            <button class="generate-btn">
                <i class="fas fa-wand-magic-sparkles"></i>
                生成 PPT
            </button>
            
            <h3>模板卡片样式</h3>
            <div class="template-grid">
                <div class="template-card selected">
                    <i class="fas fa-briefcase"></i>
                    <h4>商务模板</h4>
                    <p>适用于商务演示和报告</p>
                </div>
                <div class="template-card">
                    <i class="fas fa-graduation-cap"></i>
                    <h4>教育模板</h4>
                    <p>适用于教学和培训场景</p>
                </div>
            </div>
            
            <h3>PPT预览器样式</h3>
            <div class="ppt-viewer" style="height: 300px;">
                <div class="ppt-toolbar">
                    <div class="toolbar-left">
                        <button class="btn-icon">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <span class="slide-counter">1 / 3</span>
                        <button class="btn-icon">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                    <div class="toolbar-center">
                        <button class="btn-icon">
                            <i class="fas fa-play"></i>
                        </button>
                    </div>
                    <div class="toolbar-right">
                        <button class="btn-icon">
                            <i class="fas fa-expand"></i>
                        </button>
                    </div>
                </div>
                <div class="ppt-main">
                    <div class="slide-container">
                        <div class="slide active">
                            <div class="slide-content">
                                <h1 class="slide-title">测试幻灯片</h1>
                                <div class="slide-body">
                                    <p>这是一个样式测试幻灯片</p>
                                    <ul>
                                        <li>测试项目 1</li>
                                        <li>测试项目 2</li>
                                    </ul>
                                </div>
                                <div class="keyword-tags">
                                    <span class="keyword-tag">测试</span>
                                    <span class="keyword-tag">样式</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>快速操作</h2>
            <button onclick="window.open('/', '_blank')" class="generate-btn">
                <i class="fas fa-home"></i>
                返回主应用
            </button>
            <button onclick="location.reload()" class="generate-btn">
                <i class="fas fa-sync-alt"></i>
                刷新页面
            </button>
        </div>
    </div>

    <script>
        // 检查CSS文件加载状态
        function checkCSSLoading() {
            const mainCssStatus = document.getElementById('mainCssStatus');
            const pptCssStatus = document.getElementById('pptCssStatus');
            const fontAwesomeStatus = document.getElementById('fontAwesomeStatus');
            
            // 检查主样式文件
            const testElement = document.createElement('div');
            testElement.className = 'generate-btn';
            testElement.style.display = 'none';
            document.body.appendChild(testElement);
            
            const computedStyle = window.getComputedStyle(testElement);
            const hasMainCSS = computedStyle.background.includes('linear-gradient') || 
                              computedStyle.backgroundColor !== 'rgba(0, 0, 0, 0)';
            
            mainCssStatus.className = `status-indicator ${hasMainCSS ? 'status-success' : 'status-error'}`;
            document.body.removeChild(testElement);
            
            // 检查PPT预览样式
            const pptTestElement = document.createElement('div');
            pptTestElement.className = 'ppt-viewer';
            pptTestElement.style.display = 'none';
            document.body.appendChild(pptTestElement);
            
            const pptStyle = window.getComputedStyle(pptTestElement);
            const hasPPTCSS = pptStyle.background !== 'rgba(0, 0, 0, 0)' || 
                             pptStyle.borderRadius !== '0px';
            
            pptCssStatus.className = `status-indicator ${hasPPTCSS ? 'status-success' : 'status-error'}`;
            document.body.removeChild(pptTestElement);
            
            // 检查Font Awesome
            const iconTest = document.querySelector('.fas');
            const iconStyle = window.getComputedStyle(iconTest, ':before');
            const hasFontAwesome = iconStyle.fontFamily.includes('Font Awesome') || 
                                  iconTest.offsetWidth > 0;
            
            fontAwesomeStatus.className = `status-indicator ${hasFontAwesome ? 'status-success' : 'status-warning'}`;
        }
        
        // 页面加载完成后检查
        window.addEventListener('load', () => {
            setTimeout(checkCSSLoading, 500);
        });
        
        console.log('样式测试页面已加载');
        console.log('当前URL:', window.location.href);
        console.log('文档字符集:', document.characterSet);
    </script>
</body>
</html>
