#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI PPT Generator - NLP处理模块
使用自然语言处理技术分析用户输入文本
"""

import sys
import json
import re
from typing import Dict, List, Any

# 尝试导入高级NLP库，如果没有则使用基础实现
try:
    import spacy
    nlp = spacy.load("zh_core_web_sm")  # 中文模型
    USE_SPACY = True
except ImportError:
    USE_SPACY = False
    print("警告: spaCy未安装，使用基础NLP实现", file=sys.stderr)

def extract_keywords_basic(text: str) -> List[str]:
    """基础关键词提取（不依赖外部库）"""
    # 移除标点符号和特殊字符
    clean_text = re.sub(r'[^\w\s]', '', text)
    words = clean_text.split()

    # 简单的停用词列表
    stop_words = {
        '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个',
        '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好',
        '自己', '这', '那', '里', '就是', '可以', '这个', '那个', '什么', '怎么'
    }

    # 过滤停用词并统计词频
    word_freq = {}
    for word in words:
        if len(word) > 1 and word not in stop_words:
            word_freq[word] = word_freq.get(word, 0) + 1

    # 按频率排序，返回前10个关键词
    keywords = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)
    return [word for word, freq in keywords[:10]]

def extract_keywords_spacy(text: str) -> List[str]:
    """使用spaCy进行关键词提取"""
    doc = nlp(text)
    keywords = []

    for token in doc:
        if (token.pos_ in ['NOUN', 'PROPN', 'ADJ'] and
            not token.is_stop and
            not token.is_punct and
            len(token.text) > 1):
            keywords.append(token.text)

    # 去重并返回前10个
    return list(dict.fromkeys(keywords))[:10]

def analyze_structure(text: str) -> Dict[str, Any]:
    """分析文本结构"""
    lines = text.strip().split('\n')
    lines = [line.strip() for line in lines if line.strip()]

    structure = {
        'title': '',
        'subtitle': '',
        'sections': []
    }

    if not lines:
        return structure

    # 第一行作为标题
    structure['title'] = lines[0]

    # 如果有第二行且较短，作为副标题
    if len(lines) > 1 and len(lines[1]) < len(lines[0]) * 0.8:
        structure['subtitle'] = lines[1]
        content_start = 2
    else:
        content_start = 1

    # 分析内容段落
    current_section = None
    for i, line in enumerate(lines[content_start:], content_start):
        # 检测是否为标题行（较短或包含数字编号）
        if (len(line) < 50 and
            (re.match(r'^\d+[.、]', line) or
             re.match(r'^[一二三四五六七八九十]+[.、]', line) or
             i == content_start)):

            if current_section:
                structure['sections'].append(current_section)

            current_section = {
                'title': line,
                'content': [],
                'keywords': []
            }
        else:
            if current_section:
                current_section['content'].append(line)
            else:
                # 如果没有明确的段落标题，创建一个默认段落
                current_section = {
                    'title': '主要内容',
                    'content': [line],
                    'keywords': []
                }

    # 添加最后一个段落
    if current_section:
        structure['sections'].append(current_section)

    # 为每个段落提取关键词
    for section in structure['sections']:
        section_text = ' '.join(section['content'])
        if USE_SPACY:
            section['keywords'] = extract_keywords_spacy(section_text)[:5]
        else:
            section['keywords'] = extract_keywords_basic(section_text)[:5]

        # 将内容列表转换为字符串
        section['content'] = '\n'.join(section['content'])

    return structure

def analyze_sentiment(text: str) -> str:
    """简单的情感分析"""
    positive_words = ['好', '优秀', '成功', '增长', '提升', '改善', '创新', '机会', '优势']
    negative_words = ['问题', '困难', '挑战', '下降', '减少', '风险', '威胁', '缺点']

    positive_count = sum(1 for word in positive_words if word in text)
    negative_count = sum(1 for word in negative_words if word in text)

    if positive_count > negative_count:
        return 'positive'
    elif negative_count > positive_count:
        return 'negative'
    else:
        return 'neutral'

def process_text(text: str) -> Dict[str, Any]:
    """主要的文本处理函数"""
    try:
        # 提取关键词
        if USE_SPACY:
            keywords = extract_keywords_spacy(text)
        else:
            keywords = extract_keywords_basic(text)

        # 分析文本结构
        structure = analyze_structure(text)

        # 情感分析
        sentiment = analyze_sentiment(text)

        return {
            'keywords': keywords,
            'structure': structure,
            'sentiment': sentiment,
            'word_count': len(text.split()),
            'char_count': len(text)
        }

    except Exception as e:
        return {
            'error': str(e),
            'keywords': [],
            'structure': {'title': '错误', 'subtitle': '', 'sections': []},
            'sentiment': 'neutral'
        }

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print(json.dumps({'error': '请提供要分析的文本'}, ensure_ascii=False))
        sys.exit(1)

    try:
        # 处理转义字符和编码问题
        input_text = sys.argv[1]

        # 尝试修复可能的编码问题
        if isinstance(input_text, bytes):
            input_text = input_text.decode('utf-8', errors='ignore')

        # 处理转义字符
        input_text = input_text.replace('\\n', '\n').replace('\\t', '\t')

        result = process_text(input_text)
        print(json.dumps(result, ensure_ascii=False, indent=2))

    except UnicodeDecodeError as e:
        error_result = {
            'error': f'文本编码错误: {str(e)}',
            'keywords': [],
            'structure': {'title': '编码错误', 'subtitle': '', 'sections': []},
            'sentiment': 'neutral'
        }
        print(json.dumps(error_result, ensure_ascii=False, indent=2))
    except Exception as e:
        error_result = {
            'error': f'处理错误: {str(e)}',
            'keywords': [],
            'structure': {'title': '处理错误', 'subtitle': '', 'sections': []},
            'sentiment': 'neutral'
        }
        print(json.dumps(error_result, ensure_ascii=False, indent=2))

if __name__ == '__main__':
    main()
